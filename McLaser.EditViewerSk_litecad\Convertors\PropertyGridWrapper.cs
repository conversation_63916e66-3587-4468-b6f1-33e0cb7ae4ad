﻿using System;
using System.ComponentModel;
using System.Linq;

namespace McLaser.EditViewerSk.Convertors
{
    public class PropertyGridWrapper : ICustomTypeDescriptor
    {
        private readonly object _wrappedObject;

        public PropertyGridWrapper(object wrappedObject)
        {
            _wrappedObject = wrappedObject;
        }

        public AttributeCollection GetAttributes() => TypeDescriptor.GetAttributes(_wrappedObject);

        public string GetClassName() => TypeDescriptor.GetClassName(_wrappedObject);

        public string GetComponentName() => TypeDescriptor.GetComponentName(_wrappedObject);

        public TypeConverter GetConverter() => TypeDescriptor.GetConverter(_wrappedObject);

        public EventDescriptor GetDefaultEvent() => TypeDescriptor.GetDefaultEvent(_wrappedObject);

        public PropertyDescriptor GetDefaultProperty() => TypeDescriptor.GetDefaultProperty(_wrappedObject);

        public object GetEditor(Type editorBaseType) => TypeDescriptor.GetEditor(_wrappedObject, editorBaseType);

        public EventDescriptorCollection GetEvents(Attribute[] attributes) => TypeDescriptor.GetEvents(_wrappedObject, attributes);

        public EventDescriptorCollection GetEvents() => TypeDescriptor.GetEvents(_wrappedObject);

        public PropertyDescriptorCollection GetProperties(Attribute[] attributes)
        {
            var properties = TypeDescriptor.GetProperties(_wrappedObject, attributes);
            return WrapProperties(properties);
        }

        public PropertyDescriptorCollection GetProperties()
        {
            var properties = TypeDescriptor.GetProperties(_wrappedObject);
            return WrapProperties(properties);
        }

        public object GetPropertyOwner(PropertyDescriptor pd) => _wrappedObject;

        private PropertyDescriptorCollection WrapProperties(PropertyDescriptorCollection originalProps)
        {
            var newProps = originalProps.Cast<PropertyDescriptor>().Select(prop =>
            {
                if (prop.PropertyType == typeof(bool))
                {
                    return TypeDescriptor.CreateProperty(
                        prop.ComponentType,
                        prop,
                        new Attribute[] { new TypeConverterAttribute(typeof(BooleanConverter)) });
                }

                return prop;
            }).ToArray();

            return new PropertyDescriptorCollection(newProps);
        }
    }
}
