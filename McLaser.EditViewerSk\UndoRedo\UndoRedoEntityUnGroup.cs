﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;

namespace McLaser.EditViewerSk.UndoRedo
{
    internal class UndoRedoEntityUnGroup : UndoRedoSingle
    {
        private DocumentBase doc;
        private List<EntityBase> list;
        private List<EntityBase> groupList;
        private List<EntityBase> items;
        private EntityLayer layer;
        private UndoRedoMultiple urs;

        public override void Execute()
        {
            this.groupList = new List<EntityBase>();
            this.items = new List<EntityBase>();
            foreach (EntityBase entity in this.list)
            {
                if (entity is EntityGroup)
                {
                    EntityGroup group = entity as EntityGroup;
                    this.groupList.Add((EntityBase)group);
                    foreach (var item in group.Entities)
                    {
                        if(item == group.Entities.First())
                        {
                            item.Arg.BeforeBuffer = group.Arg.BeforeBuffer;
                        }
                        else if (item == group.Entities.Last())
                        {
                            item.Arg.AfterBuffer = group.Arg.AfterBuffer;
                        }
                    }
                    this.items.AddRange((IEnumerable<EntityBase>)(group.Entities));
                   
                }
            }
            this.Redo();
        }

        public override void Undo() => this.urs.Undo();

        public override void Redo()
        {
            this.urs = new UndoRedoMultiple();
            this.urs.Add((IUndoRedo)new UndoRedoEntityDelete(this.doc, this.groupList));
            var index = groupList[0].Index-1;
            foreach (EntityBase entity in this.items)
                this.urs.Add((IUndoRedo)new UndoRedoEntityAdd(this.doc, this.layer, new List<EntityBase>() { entity }, index++));
            this.urs.Execute();
        }

        public UndoRedoEntityUnGroup(DocumentBase doc, List<EntityBase> list, EntityLayer layer)
        {
            this.Name = "Entity Ungroup";
            this.doc = doc;
            this.list = new List<EntityBase>((IEnumerable<EntityBase>)list);
            this.layer = layer;
        }
    }
}
