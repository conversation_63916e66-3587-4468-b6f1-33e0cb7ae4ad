﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Core;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using System;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.UndoRedo
{
    internal class UndoRedoEntityMove : UndoRedoSingle
    {
        private DocumentBase doc;
        private EntityBase entity;
        private EntityLayer sourceLayer;
        private EntityLayer targetLayer;
        private int targetIndex;
        private EntityBase cloned;
        private UndoRedoEntityDelete ur;

        public override void Execute() => this.Redo();

        public override void Undo()
        {
            this.ur.Undo();
            this.targetLayer.Remove(this.cloned);
        }

        public override void Redo()
        {
            if (this.entity is EntityLayer)
                return;
            this.sourceLayer = (EntityLayer)this.entity.Parent;
            EntityBase entity1 = this.entity;
            int num = this.entity.Parent.Children.IndexOf(this.entity);
            if (!(this.entity is ICloneable entity2))
                return;
            this.cloned = (EntityBase)entity2.Clone();
            if (this.sourceLayer.Equals((object)this.targetLayer))
            {
                if (num > this.targetIndex)
                    this.targetLayer.Insert(this.targetIndex, this.cloned);
                else
                    this.targetLayer.Insert(this.targetIndex + 1, this.cloned);
            }
            else
                this.targetLayer.Insert(this.targetIndex, this.cloned);
            this.ur = new UndoRedoEntityDelete(this.doc, new List<EntityBase>()
      {
        this.entity
      });
            this.ur.Execute();
        }

        public UndoRedoEntityMove(DocumentBase doc, EntityBase entity, EntityLayer targetLayer, int targetIndex)
        {
            this.Name = "Entity Move";
            this.doc = doc;
            this.entity = entity;
            this.targetLayer = targetLayer;
            this.targetIndex = targetIndex;
        }
    }
}
