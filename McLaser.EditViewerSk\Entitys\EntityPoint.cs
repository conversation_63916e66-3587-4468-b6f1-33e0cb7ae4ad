﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityPoint : EntityBase
    {
        public EntityPoint()
        {
            Name = "Point";
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/201.png"));

        }

        public EntityPoint(Vector2 endPnt)
        {
            Name = "Point";
            EndPoint = endPnt;
            Icon = new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/201.png"));

        }

        private Vector2 _endPoint = new Vector2();
        [Category("基础"), DisplayName("终点")]
        public Vector2 EndPoint
        {
            get { return _endPoint; }
            set { _endPoint = value; OnPropertyChanged("endPoint"); }
        }

    

       
        private BoundingBox _boundingBox;
        public override BoundingBox BoundingBox
        {
            get
            {
                double left = this.EndPoint.X - 5;
                double right = this.EndPoint.X + 5;
                double top = this.EndPoint.Y + 5;
                double bottom = this.EndPoint.Y - 5;
                this._boundingBox = new BoundingBox(left, top, right, bottom);
                return this._boundingBox;
            }
        }

        public override void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;
            (view as ViewBase).DrawPoint(_endPoint, Pen);
        }

        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return MathHelper.IntersectPointInRect(new BoundingBox(left, top, right, bottom), EndPoint.X, EndPoint.Y);
        }

        /// <summary>
        /// 对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            List<ObjectSnapPoint> snapPnts = new List<ObjectSnapPoint>
            {
                new ObjectSnapPoint(ObjectSnapMode.End, _endPoint)
            };

            return snapPnts;
        }


        public override void Translate(Vector2 translation)
        {
            _endPoint += translation;
        }

        
    }
}
