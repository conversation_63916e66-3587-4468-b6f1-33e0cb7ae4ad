﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Dxf;
using SkiaSharp;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace McLaser.EditViewerSk.Common
{


    public class AnchorsMgr
    {
        private ViewBase _viewer = null;

        private Dictionary<ObjectId, List<GripPoint>> _gripPnts = new Dictionary<ObjectId, List<GripPoint>>();
        private GripPoint _currGripPoint = null;
        public GripPoint currentGripPoint
        {
            get { return _currGripPoint; }
        }
        private ObjectId _currGripEntityId = ObjectId.Null;
        public ObjectId currentGripEntityId
        {
            get { return _currGripEntityId; }
        }
        private int _currGripPointIndex = -1;
        public int currentGripPointIndex
        {
            get { return _currGripPointIndex; }
        }

        public AnchorsMgr(ViewBase presenter)
        {
            _viewer = presenter;
        }

        public void Update()
        {
            DocumentBase doc = _viewer.Document as DocumentBase;
            //if (doc.Selections.Count == 0)
            //{
            //    _gripPnts.Clear();
            //    return;
            //}

            Dictionary<ObjectId, List<GripPoint>> oldGripPnts = _gripPnts;
            _gripPnts = new Dictionary<ObjectId, List<GripPoint>>();
            //foreach (Selection sel in doc.Selections)
            //{
            //    if (sel.objectId == ObjectId.Null)
            //    {
            //        continue;
            //    }
            //    if (oldGripPnts.ContainsKey(sel.objectId))
            //    {
            //        _gripPnts[sel.objectId] = oldGripPnts[sel.objectId];
            //        continue;
            //    }

            //    DBObject dbobj = doc.Database.GetObject(sel.objectId);
            //    if (dbobj == null)
            //    {
            //        continue;
            //    }
            //    Entity entity = dbobj as Entity;
            //    if (entity == null)
            //    {
            //        continue;
            //    }

            //    List<GripPoint> entGripPnts = entity.GetGripPoints();
            //    if (entGripPnts != null && entGripPnts.Count > 0)
            //    {
            //        _gripPnts[sel.objectId] = entGripPnts;
            //    }
            //}
        }

        public void Clear()
        {
            _gripPnts.Clear();
        }

        public void OnPaint()
        {
            foreach (KeyValuePair<ObjectId, List<GripPoint>> kvp in _gripPnts)
            {
                foreach (GripPoint gripPnt in kvp.Value)
                {
                    float width = 10;
                    float height = 10;
                    Vector2 posInCanvas = _viewer.ModelToCanvas(gripPnt.position);
                    posInCanvas.X -= width / 2;
                    posInCanvas.Y -= height / 2;
                   // _presenter.FillRectangle(graphics, GDIResMgr.Instance.GetBrush(Color.Blue), posInCanvas, width, height, CSYS.Canvas);
                }
            }
        }

        public Vector2 Snap(Vector2 posInCanvas)
        {
            Vector2 posInModel = _viewer.CanvasToModel(posInCanvas);

            foreach (KeyValuePair<ObjectId, List<GripPoint>> kvp in _gripPnts)
            {
                int index = -1;
                foreach (GripPoint gripPnt in kvp.Value)
                {
                    ++index;
                    float width = 10;
                    float height = 10;
                    Vector2 gripPosInCanvas = _viewer.ModelToCanvas(gripPnt.position);
                    gripPosInCanvas.X -= width / 2;
                    gripPosInCanvas.Y -= height / 2;
         
                    //LitMath.Rectangle2 rect = new LitMath.Rectangle2(gripPosInCanvas, width, height);

                    //if (MathUtils.IsPointInRectangle(posInCanvas, rect))
                    //{
                    //    _currGripPoint = gripPnt;
                    //    _currGripEntityId = kvp.Key;
                    //    _currGripPointIndex = index;
                    //    return gripPnt.position;
                    //}
                }
            }

            _currGripPoint = null;
            _currGripEntityId = ObjectId.Null;
            _currGripPointIndex = -1;
            return posInModel;
        }
    }
}
