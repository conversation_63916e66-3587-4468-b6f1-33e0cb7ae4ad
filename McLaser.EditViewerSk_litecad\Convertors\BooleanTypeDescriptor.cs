﻿using System;
using System.ComponentModel;

namespace McLaser.EditViewerSk.Convertors
{
    public class BooleanTypeDescriptor : CustomTypeDescriptor
    {
        public BooleanTypeDescriptor(ICustomTypeDescriptor parent) : base(parent) { }

        public override PropertyDescriptorCollection GetProperties(Attribute[] attributes)
        {
            PropertyDescriptorCollection props = base.GetProperties(attributes);
            return ModifyProperties(props);
        }

        public override PropertyDescriptorCollection GetProperties()
        {
            PropertyDescriptorCollection props = base.GetProperties();
            return ModifyProperties(props);
        }

        private static PropertyDescriptorCollection ModifyProperties(PropertyDescriptorCollection props)
        {
            PropertyDescriptorCollection newProps = new PropertyDescriptorCollection(null);

            foreach (PropertyDescriptor prop in props)
            {
                if (prop.PropertyType == typeof(bool))
                {
                    var newProp = TypeDescriptor.CreateProperty(
                        prop.ComponentType,
                        prop,
                        new Attribute[] { new TypeConverterAttribute(typeof(BooleanConverter)) }
                    );
                    newProps.Add(newProp);
                }
                else
                {
                    newProps.Add(prop);
                }
            }

            return newProps;
        }
    }
}
