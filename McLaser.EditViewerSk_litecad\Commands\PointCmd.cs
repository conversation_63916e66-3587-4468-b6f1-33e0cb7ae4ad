﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;

namespace McLaser.EditViewerSk.Commands
{
    public class PointCmd : DrawCmd
    {
        private EntityPoint _point = null;
        private DocumentBase doc;


        public PointCmd(DocumentBase doc)
        {
                this.doc = doc; 
        }


        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityPoint[1] { _point }; }
        }

        public override void Initialize()
        {
            base.Initialize();

            this.pointer.Mode = IndicatorMode.Locate;
        }

        protected override void Commit()
        {
            if (this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                _point = new EntityPoint(this.pointer.CurrentSnapPoint);
                //_point.color = this.Document.currentColor;
                //_point.layerId = this.Document.currentLayerId;
                _mgr.FinishCurrentCommand();
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            //if (_point != null)
            //{
            //    ViewBase viewer = _mgr.Viewer as ViewBase;
            //    _point.Render(viewer);
            //}
        }
    }
}
