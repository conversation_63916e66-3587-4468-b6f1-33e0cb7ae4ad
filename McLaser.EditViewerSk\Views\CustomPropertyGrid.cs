﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Views
{
    public class CustomPropertyGrid : PropertyGrid
    {
        protected override void OnSelectedObjectsChanged(EventArgs e)
        {
            base.OnSelectedObjectsChanged(e);
            if (SelectedObject != null)
            {

                var propertyDescriptors = TypeDescriptor.GetProperties(SelectedObject);
                foreach (PropertyDescriptor propertyDescriptor in propertyDescriptors)
                {
                    if (propertyDescriptor.PropertyType == typeof(bool))
                    {

                        PropertyDescriptor newPropertyDescriptor = TypeDescriptor.CreateProperty(
                            propertyDescriptor.ComponentType,
                            propertyDescriptor,
                            new Attribute[] { new TypeConverterAttribute(typeof(BooleanConverter)) }
                        );


                        ((ICustomTypeDescriptor)SelectedObject).GetProperties().Add(newPropertyDescriptor);
                    }
                }
            }
        }
    }
}
