﻿<Window x:Class="McLaser.EditViewerSk.Views.CardSettingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:McLaser.EditViewerSk.Views"
        mc:Ignorable="d"
        Title="CardSettingWindow" Height="750" Width="1000" WindowStartupLocation="CenterScreen" ResizeMode="NoResize" >
    <Grid>
        <TabControl >
            <TabItem Header="轴参数" Width="100">
                <ContentControl Content="{Binding ViewAxisConfig}"/>
            </TabItem>

           
            <TabItem Header="状态" Width="100">
                <ContentControl Content="{Binding ViewCardStatus}"/>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
