﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Dxf;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Input;
using McLaser.EditViewerSk.Input;
using System.Windows.Forms;
using McLaser.EditViewerSk.Entitys;
using MouseEventArgs = System.Windows.Input.MouseEventArgs;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using SkiaSharp;
using System.Linq;
using System.Collections.ObjectModel;
using System.Numerics;

namespace McLaser.EditViewerSk.Commands
{
    internal class LinesChainCmd : DrawCmd
    {
        private List<EntityLine> _lines = new List<EntityLine>();
        private EntityLine _currLine = null;
        private DocumentBase doc;


        public LinesChainCmd(DocumentBase doc)
        {
                this.doc = doc; 
        }
        protected override IEnumerable<EntityBase> newEntities
        {
            get { return _lines.ToArray(); }
        }

      
        private enum Step
        {
            Step1_SpecifyStartPoint = 1,
            Step2_SpecifyEndPoint = 2,
        }
        private Step _step = Step.Step1_SpecifyStartPoint;

     
        private DynInputPoint _pointInput = null;
 
        public override void Initialize()
        {
            base.Initialize();

            //
            _step = Step.Step1_SpecifyStartPoint;
            this.pointer.Mode = IndicatorMode.Locate;

            _pointInput = new DynInputPoint(this._viewer, new Vector2(0, 0));
            _pointInput.Message = "指定第一个点: ";
            this.dynamicInputer.StartInput(_pointInput);
            _pointInput.finish += this.OnPointInputReturn;
            _pointInput.cancel += this.OnPointInputReturn;
        }

       
        public override void Terminate()
        {
            _pointInput.finish -= this.OnPointInputReturn;
            _pointInput.cancel -= this.OnPointInputReturn;

            base.Terminate();
        }


        protected override void Commit()
        {
            if(this.newEntities.Count()>1)
            {
                EntityGroup group = new EntityGroup();
                group.Children = new System.Collections.ObjectModel.ObservableCollection<EntityBase>(newEntities);
                doc.Action.ActEntityAdd(group);
            }
            else if(this.newEntities.Count()==1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }


        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            if (_step == Step.Step1_SpecifyStartPoint)
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    _currLine = new EntityLine();
                    _currLine.StartPoint = this.pointer.CurrentSnapPoint;
                    _currLine.EndPoint = this.pointer.CurrentSnapPoint;
                 

                    _pointInput.Message = "指定下一点: ";
                    _step = Step.Step2_SpecifyEndPoint;
                }
            }
            else if (_step == Step.Step2_SpecifyEndPoint)
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    _currLine.EndPoint = this.pointer.CurrentSnapPoint;
                   
                    _lines.Add(_currLine);

                    _currLine = new EntityLine();
                    _currLine.StartPoint = this.pointer.CurrentSnapPoint;
                    _currLine.EndPoint = this.pointer.CurrentSnapPoint;
                    
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (e.MiddleButton == MouseButtonState.Pressed)
            {
                return EventResult.Handled;
            }

            if (_currLine != null)
            {
                _currLine.EndPoint = this.pointer.CurrentSnapPoint;
                _viewer.RepaintCanvas();
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                if (_lines.Count > 0)
                {
                    _mgr.FinishCurrentCommand();
                }
                else
                {
                    _mgr.CancelCurrentCommand();
                }
            }
            return EventResult.Handled;
        }

        public override EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            //foreach (EntityLine line in _lines)
            //{
            //    ViewBase viewer = _mgr.Viewer as ViewBase;
            //    line.Render(viewer);
            //}

            //if (_currLine != null)
            //{
            //    ViewBase viewer = _mgr.Viewer as ViewBase;
            //    _currLine.Render(viewer);
            //}
        }

        private void OnPointInputReturn(DynInputCtrl sender, DynInputResult retult)
        {
            DynInputResult<Vector2> xyRet = retult as DynInputResult<Vector2>;
            if (xyRet == null
                || xyRet.status == DynInputStatus.Cancel)
            {
                if (_lines.Count > 0)
                {
                    _mgr.FinishCurrentCommand();
                }
                else
                {
                    _mgr.CancelCurrentCommand();
                }

                return;
            }

            _pointInput.Message = "指定下一点: ";
            this.dynamicInputer.StartInput(_pointInput);

            switch (_step)
            {
                case Step.Step1_SpecifyStartPoint:
                    {
                        _currLine = new EntityLine();
                        _currLine.StartPoint = xyRet.value;
                        _currLine.EndPoint = xyRet.value;
                        //_currLine.layerId = this.Document.currentLayerId;
                        //_currLine.color = this.Document.currentColor;

                        _step = Step.Step2_SpecifyEndPoint;
                    }
                    break;

                case Step.Step2_SpecifyEndPoint:
                    {
                        _currLine.EndPoint = xyRet.value;
                        //_currLine.layerId = this.Document.currentLayerId;
                        //_currLine.color = this.Document.currentColor;
                        _lines.Add(_currLine);

                        _currLine = new EntityLine();
                        _currLine.StartPoint = xyRet.value;
                        _currLine.EndPoint = xyRet.value;
                        //_currLine.layerId = this.Document.currentLayerId;
                        //_currLine.color = this.Document.currentColor;
                    }
                    break;
            }
        }
    }
}
