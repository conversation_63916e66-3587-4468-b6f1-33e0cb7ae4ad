#region McLaser library licensed under the MIT License
// 
//                       <PERSON><PERSON><PERSON><PERSON><PERSON> library
// Copyright (c) <PERSON> (<EMAIL>)
// 
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
// 
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
// 
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.
// 
#endregion

using System;

namespace McLaser.EditViewerSk.Dxf.Entities
{
    /// <summary>
    /// Defines the Face3D hidden edges.
    /// </summary>
    [Flags]
    public enum Face3DEdgeFlags
    {
        /// <summary>
        /// No flags equivalent to all edges are visible.
        /// </summary>
        None = 0,

        /// <summary>
        /// First edge is invisible.
        /// </summary>
        First = 1,

        /// <summary>
        /// Second edge is invisible.
        /// </summary>
        Second = 2,

        /// <summary>
        /// Third edge is invisible.
        /// </summary>
        Third = 4,

        /// <summary>
        /// Fourth edge is invisible.
        /// </summary>
        Fourth = 8
    }
}