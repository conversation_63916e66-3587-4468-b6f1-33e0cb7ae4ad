﻿using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Input;
using McLaser.EditViewerSk.Interfaces;
using System.Windows.Input;
using ICommand = McLaser.EditViewerSk.Interfaces.ICommand;

namespace McLaser.EditViewerSk.Base
{


    public abstract class Command : ICommand
    {
       
        protected CommandsMgr _mgr = null;
        public CommandsMgr cmdMgr
        {
            get { return _mgr; }
            set { _mgr = value; }
        }

        public ViewBase _viewer
        {
            get {  return _mgr?.Viewer; }
        }

        public DocumentBase Document
        {
            get { return _mgr?.Viewer.Document as DocumentBase; }
        }

        //public Database database
        //{
        //    get { return (_mgr?.Viewer.Document as DocumentBase).Database; }
        //}

        public MgrIndicator pointer
        {
            get { return _mgr?.Viewer._pointer; }
        }

        public DynamicInputer dynamicInputer
        {
            get { return _mgr?.Viewer._dynamicInputer; }
        }

        protected IndicatorMode _lastPointerMode;
        protected bool _lastShowAnchor;

       
        public virtual void Initialize()
        {
            _lastPointerMode = this.pointer.Mode;
            _lastShowAnchor = this.pointer.bIsShowAnchor;

            this.pointer.bIsShowAnchor = false;
        }

  
        public virtual void Terminate()
        {
            this.pointer.Mode = _lastPointerMode;
            this.pointer.bIsShowAnchor = _lastShowAnchor;
        }

        public virtual void Undo()
        {
            this.Rollback();
        }

  
        public virtual void Redo()
        {
            this.Commit();
        }

 
        public virtual void Finish()
        {
            this.Commit();
            this.Terminate();
        }

 
        public virtual void Cancel()
        {
            this.Terminate();
        }

   
        protected virtual void Commit()
        {
        }

     
        protected virtual void Rollback()
        {
        }

       
        public virtual EventResult OnMouseDown(MouseEventArgs e)
        {
            return EventResult.Unhandled;
        }

 
        public virtual EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Unhandled;
        }

   
        public virtual EventResult OnMouseMove(MouseEventArgs e)
        {
            return EventResult.Unhandled;
        }

   
        public virtual EventResult OnMouseWheel(MouseEventArgs e)
        {
            return EventResult.Unhandled;
        }

        public virtual EventResult OnKeyDown(KeyEventArgs e)
        {
            return EventResult.Unhandled;
        }

  
        public virtual EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Unhandled;
        }

        public virtual void OnPaint(ViewBase _viewer)
        {
        }

  
        public class EventResult
        {
            public EventResultStatus status = EventResultStatus.Invalid;
            public object data = null;

            public static EventResult Unhandled
            {
                get
                {
                    EventResult eRet = new EventResult();
                    eRet.status = EventResultStatus.Unhandled;
                    return eRet;
                }
            }

            public static EventResult Handled
            {
                get
                {
                    EventResult eRet = new EventResult();
                    eRet.status = EventResultStatus.Handled;
                    return eRet;
                }
            }
        }

   
        public enum EventResultStatus
        {
            // 无效
            Invalid = 0,
            // 处理了
            Handled = 1,
            // 未处理
            Unhandled = 2,
        }
    }


    public class CommandNames
    {
        // 绘制
        public static string Draw_XPoint = "xpoint";
        public static string Draw_Line = "line";
        public static string Draw_Xline = "xline";
        public static string Draw_Ray = "ray";
        public static string Draw_Polyline = "polyline";
        public static string Draw_Polygon = "polygon";
        public static string Draw_Rectangle = "rectangle";
        public static string Draw_Circle = "circle";
        public static string Draw_Ellipse = "ellipse";
        public static string Draw_Arc = "arc";

        // 编辑
        public static string Edit_Redo = "redo";
        public static string Edit_Undo = "undo";

        // 修改
        public static string Modify_Delete = "delete";
        public static string Modify_Copy = "copy";
        public static string Modify_Mirror = "mirror";
        public static string Modify_Offset = "offset";
        public static string Modify_Move = "move";
    }


}
