﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;

namespace McLaser.EditViewerSk.Commands
{
    internal class PolylineCmd : DrawCmd
    {
        public EntityLine CurLine = null;
        private EntityLwPolyline _polyline = null;
        private DocumentBase doc;


        public PolylineCmd(DocumentBase doc)
        {
                this.doc = doc;
        }


        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityLwPolyline[1] { _polyline }; }
        }

        /// <summary>
        /// 步骤
        /// </summary>
        private enum Step
        {
            Step1_SpecifyStartPoint = 1,
            Step2_SpecifyOtherPoint = 2,
        }
        private Step _step = Step.Step1_SpecifyStartPoint;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            //
            _step = Step.Step1_SpecifyStartPoint;
            this.pointer.Mode = IndicatorMode.Locate;
        }


        protected override void Commit()
        {
            if (this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }


        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            if (_step == Step.Step1_SpecifyStartPoint)
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    _polyline = new EntityLwPolyline();
                    _polyline.Vertexes.Add(this.pointer.CurrentSnapPoint);
           

                    CurLine = new EntityLine();
                    CurLine.StartPoint = CurLine.EndPoint = this.pointer.CurrentSnapPoint;

                    _step = Step.Step2_SpecifyOtherPoint;
                }
            }
            else if (_step == Step.Step2_SpecifyOtherPoint)
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    _polyline.Vertexes.Add(this.pointer.CurrentSnapPoint);
                    CurLine.StartPoint = this.pointer.CurrentSnapPoint;
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (e.MiddleButton == MouseButtonState.Pressed)
            {
                return EventResult.Handled;
            }

            if (_step == Step.Step2_SpecifyOtherPoint)
            {
                if (CurLine != null)
                {
                    CurLine.EndPoint = this.pointer.CurrentSnapPoint;
                }
            }

            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                if (_polyline.Vertexes.Count > 1)
                {
                    _mgr.FinishCurrentCommand();
                }
                else
                {
                    _mgr.CancelCurrentCommand();
                }
            }
            return EventResult.Handled;
        }

        public override EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            //if (CurLine != null)
            //{
            //    CurLine.Render(_viewer);
            //}
            //if (_polyline != null)
            //{
            //    ViewBase viewer = _mgr.Viewer as ViewBase;
            //    _polyline.Render(viewer);
            //}
        }
    }
}
