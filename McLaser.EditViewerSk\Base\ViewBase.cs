﻿using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Input;
using McLaser.EditViewerSk.Interfaces;
using OpenTK;
using SkiaSharp;
using SkiaSharp.Views.Desktop;
using SkiaSharp.Views.WPF;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using Point = System.Windows.Point;
using ICommand = McLaser.EditViewerSk.Interfaces.ICommand;
using System.Windows.Input;



namespace McLaser.EditViewerSk.Base
{

    public class ViewBase : IView
    {

        //图档
        public DocumentBase Document { get; set; }

        //SkiaSharp 控件
        internal SKElement Viewer;

        //SkiaSharp 绘图画板
        internal SKCanvas Canvas;

        //默认的绘图参数Pen 
        internal SKPaint DefaultPen = new SKPaint()
        {
            Color = SKColors.LightGray,
            StrokeWidth = 1f,
            Style = SKPaintStyle.Stroke,
            IsAntialias = true
        };


        //命令管理器
        public CommandsMgr _cmdsMgr = null;

        //指针管理器
        public MgrIndicator _pointer = null;

        //动态输入管理器
        public DynamicInputer _dynamicInputer = null;

        public List<EntityBase> Selections => Document.SelectedEntitys;

        //选中Entity
        public EntityBase HittedEntity { get; set; }

        //Undo
        public bool CanUndo => _cmdsMgr.CanUndo;

        //Redo
        public bool CanRedo => _cmdsMgr.CanRedo;

        //宽
        public double Width => Viewer.ActualWidth;

        //高
        public double Height => Viewer.ActualHeight;

        public List<ObjectSnapPoint> GridSnaps = new List<ObjectSnapPoint>();

        //渲染时间
        public virtual long RenderTime { get; protected set; }

        //渲染时间计数器
        private Stopwatch _stopwatch = new Stopwatch();

        //选中框
        private BoundingBox SelectedBoundRect { get; set; } = new BoundingBox();
        private Vector2 _screenPan = new Vector2();
        private Vector2 _screenDrag = new Vector2();

        private Vector2 _mouseDownPoint = new Vector2();
        private CommandsFactory _cmdsFactory = new CommandsFactory();

        private Point mouseCurrentLocation;
        private Point mouseLeftDownLocation;

        private bool drawPanning = false;
        private SKMatrix _matrixTrans = SKMatrix.CreateScale(1, -1);

        public EventHandler<MouseEventArgs> MouseMove;

        double _x = 0, _y = 0;

        //构造函数
        public ViewBase(SKElement element, DocumentBase doc)
        {
            Viewer = element;
            Document = doc;
            SelectedBoundRect = BoundingBox.Empty;

            _cmdsMgr = new CommandsMgr(this);
            _cmdsMgr.commandFinished += this.OnCommandFinished;
            _cmdsMgr.commandCanceled += this.OnCommandCanceled;
            _dynamicInputer = new DynamicInputer(this);
            _dynamicInputer.cmdInput.finish += this.OnCmdInputResurn;
            _dynamicInputer.cmdInput.cancel += this.OnCmdInputResurn;
            _pointer = new MgrIndicator(this);


            Initialize(Viewer);
            InitGridSnaps();
        }

        public ViewBase(SKElement element)
        {
            Viewer = element;
            SelectedBoundRect = BoundingBox.Empty;
            _cmdsMgr = new CommandsMgr(this);
            _cmdsMgr.commandFinished += this.OnCommandFinished;
            _cmdsMgr.commandCanceled += this.OnCommandCanceled;
            _dynamicInputer = new DynamicInputer(this);
            _dynamicInputer.cmdInput.finish += this.OnCmdInputResurn;
            _dynamicInputer.cmdInput.cancel += this.OnCmdInputResurn;
            _pointer = new MgrIndicator(this);
            Initialize(Viewer);
            InitGridSnaps();
        }


        //初始化
        private void Initialize(SKElement element)
        {
            element.PaintSurface += SkContainer_PaintSurface;
            element.MouseDown += SkContainer_MouseDown;
            element.MouseUp += SkContainer_MouseUp;
            element.MouseMove += SkContainer_MouseMove;
            element.MouseWheel += SkContainer_MouseWheel;
            element.KeyDown += SkContainer_KeyDown;
            element.KeyUp += SkContainer_KeyUp;
            element.MouseEnter += SkContainer_MouseEnter;
            element.MouseLeave += SkContainer_MouseLeave;
            element.SizeChanged += SkContainer_SizeChanged;
        }

        public void OnMouseMove(object sender, MouseEventArgs e)
        {
            MouseMove?.Invoke(sender as IInputElement, e);
        }


        //绘制网格
        public void InitGridSnaps()
        {
            for (int i = -500; i <= 500; i += 10)
            {
                for (int j = -500; j <= 500; j += 10)
                {
                    GridSnaps.Add(new ObjectSnapPoint(ObjectSnapMode.Grid, new Vector2(i, j)));
                }
            }
        }

        //窗口尺寸变化时
        private void SkContainer_SizeChanged(object sender, SizeChangedEventArgs e)
        {

        }

        Point _ptStart;
        //鼠标按下
        private void SkContainer_MouseDown(object sender, MouseButtonEventArgs e)
        {
            var cur = e.GetPosition(sender as IInputElement);
            var pt1 = CanvasToModel(new Vector2((float)cur.X, (float)cur.Y));
            var pt2 = ModelToCanvas(pt1);



            Command cmd = _pointer.OnMouseDown(sender as IInputElement, e);
            _mouseDownPoint.X = (float)cur.X;
            _mouseDownPoint.Y = (float)cur.Y;
            _ptStart = new Point(cur.X, cur.Y);
            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnMouseDown(e);
                RepaintCanvas(true);
            }
            else
            {
                if (cmd != null)
                {
                    _cmdsMgr.DoCommand(cmd);
                    RepaintCanvas();
                }
            }


            if (e.LeftButton.HasFlag(MouseButtonState.Pressed))
            {

                this.mouseLeftDownLocation = cur;
                this.mouseCurrentLocation = cur;
                this.drawPanning = true;
                //RepaintCanvas();

            }
            else if (e.MiddleButton.HasFlag(MouseButtonState.Pressed))
            {
                this.mouseCurrentLocation = cur;
            }

            this.mouseCurrentLocation = cur;
        }

        //鼠标弹起
        private void SkContainer_MouseUp(object sender, MouseEventArgs e)
        {
            var cur = e.GetPosition(sender as IInputElement);
            _pointer.OnMouseUp(sender as IInputElement, e);
            if (e.LeftButton.HasFlag(MouseButtonState.Pressed))
            {

            }
            else if (e.MiddleButton.HasFlag(MouseButtonState.Pressed))
            {
                this.mouseCurrentLocation = cur;
                _screenPan += _screenDrag;
                _screenDrag.X = 0;
                _screenDrag.Y = 0;
                return;
            }

            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnMouseUp(e);
            }
        }

        //鼠标移动
        private void SkContainer_MouseMove(object sender, MouseEventArgs e)
        {
            var cur = e.GetPosition(sender as IInputElement);
            OnMouseMove(sender as IInputElement, e);
            _pointer.OnMouseMove(sender as IInputElement, e);
            _dynamicInputer.OnMouseMove(sender as IInputElement, e);



            if (e.LeftButton.HasFlag(MouseButtonState.Pressed))
            {
                var position = e.GetPosition(Viewer);

            }
            else if (e.MiddleButton.HasFlag(MouseButtonState.Pressed))
            {

                float offsetX = (float)(cur.X - this.mouseCurrentLocation.X);  //屏幕上鼠标移动量
                float offsetY = (float)(cur.Y - this.mouseCurrentLocation.Y);
                _matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateTranslation(offsetX, offsetY));

                this.mouseCurrentLocation = cur;

            }


            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnMouseMove(e);
                //RepaintCanvas();
            }
            Viewer.InvalidateVisual();
        }

        //鼠标滚轮滚动
        private void SkContainer_MouseWheel(object sender, MouseWheelEventArgs e)
        {

            var zoomFactor = e.Delta > 0 ? 1.1f : 0.9f; // 缩放因子


            var mousePosition = e.GetPosition((IInputElement)sender).ToSKPoint();
            var pt = CanvasToModel(new Vector2(mousePosition.X, mousePosition.Y));
            // 将鼠标位置转换为相对平移
            _matrixTrans = _matrixTrans.PostConcat(
                SKMatrix.CreateTranslation(-mousePosition.X, -mousePosition.Y));
            _matrixTrans = _matrixTrans.PostConcat(
                SKMatrix.CreateScale(zoomFactor, zoomFactor));
            _matrixTrans = _matrixTrans.PostConcat(
                SKMatrix.CreateTranslation(mousePosition.X, mousePosition.Y));



            // 重新渲染
            Viewer.InvalidateVisual();
        }

        //鼠标进入
        private void SkContainer_MouseEnter(object sender, MouseEventArgs e)
        {
            Viewer.Focus();
        }

        //鼠标离开
        private void SkContainer_MouseLeave(object sender, MouseEventArgs e)
        {
        }

        //按键抬起
        public void SkContainer_KeyUp(object sender, KeyEventArgs e)
        {
            _pointer.OnKeyUp(e);
            if (_cmdsMgr != null)
            {
                _cmdsMgr.OnKeyUp(e);
            }

        }

        //按键按下
        public void SkContainer_KeyDown(object sender, KeyEventArgs e)
        {
            _pointer.OnKeyDown(e);
            if (_cmdsMgr.CurrentCmd != null)
            {
                _cmdsMgr.OnKeyDown(e);
            }
            else
            {
                if (_dynamicInputer.StartCmd(e))
                {
                }
                else if (e.Key == Key.Escape)
                {
                    Document.SelectedEntitys.Clear();
                }
                else if (e.Key == Key.Delete)
                {
                    if (Document.SelectedEntitys.Count > 0)
                    {
                        //DeleteCmd cmd = new DeleteCmd();
                        //this.OnCommand(cmd);
                    }
                }
            }

        }

        //渲染面绘制
        private void SkContainer_PaintSurface(object sender, SKPaintSurfaceEventArgs e)
        {
            using (Canvas = e.Surface.Canvas)
            {
                var info = e.Info;
                Canvas.Clear(SKColors.WhiteSmoke);

                DrawGrids();
                DrawAxes();
                //DrawRuler();
                DrawIndicator();
                DrawSelectedBBox();
                DrawWatermark(info.Width, info.Height);
                DrawLabel(info.Width, info.Height);
                // 正常绘制图形
                OnRender();

                SKPaint pen = new SKPaint()
                {
                    Color = SKColors.Yellow,
                    StrokeWidth = 1,
                    IsAntialias = true,
                    Style = SKPaintStyle.Fill
                };
                var pt = ModelToCanvas(new Vector2(_x, _y));

                Canvas?.DrawCircle((float)pt.X, (float)pt.Y, 5, pen);

            }
        }

        public void SetSelectedBoxPart()
        {
            if (SelectedBoundRect.Left == 0 && SelectedBoundRect.Right == 0 && SelectedBoundRect.Top == 0 && SelectedBoundRect.Bottom == 0)
            {
                return;
            }
            SetPart(new SKRect((float)SelectedBoundRect.Left, (float)SelectedBoundRect.Top, (float)SelectedBoundRect.Right, (float)SelectedBoundRect.Bottom), 200);
        }

        private void SetPart(SKRect rect, float margin)
        {
            if (rect == null)
            {
                return;
            }
            var zoomCenter = new SKPoint(rect.MidX, rect.MidY);

            // 缩放中间坐标
            var visibleWidth = Viewer.ActualWidth;  // 获取视图的宽度  
            var visibleHeight = Viewer.ActualHeight; // 获取视图的高度  
            float widthScale = 0, heightScale = 0;
            while (widthScale != 1 && heightScale != 1) // 确保放大到和视图上下左右相差margin
            {
                widthScale = (float)(visibleWidth / Math.Abs(rect.Width * _matrixTrans.ScaleX + margin));
                heightScale = (float)(visibleHeight / Math.Abs(rect.Height * _matrixTrans.ScaleY + margin));
                float scale = Math.Min(widthScale, heightScale);
                _matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateScale((float)scale, (float)scale));
            }

            // 缩放左上角坐标
            var currentZoomCenter = new SKPoint(_matrixTrans.Values[2], _matrixTrans.Values[5]);
            _matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateTranslation(-currentZoomCenter.X, -currentZoomCenter.Y));
            _matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateTranslation(-zoomCenter.X * _matrixTrans.ScaleX, -zoomCenter.Y * _matrixTrans.ScaleY));
            _matrixTrans = _matrixTrans.PostConcat(SKMatrix.CreateTranslation((float)(visibleWidth / 2), (float)(visibleHeight / 2)));

            // 重新渲染
            RepaintCanvas();
        }

        public void DrawDot(double x, double y)
        {
            _x = x;
            _y = y;
            Application.Current?.Dispatcher.Invoke(() => RepaintCanvas());
        }

        //按钮双击
        public void OnMouseDoubleClick(MouseEventArgs e)
        {
            _pointer.OnMouseDoubleClick(e);
        }

        //渲染
        private void OnRender()
        {
            _stopwatch.Restart();
            //绘制图档
            Document?.Render(this);
            //绘制指示器
            _cmdsMgr.OnPaint(this);
            _stopwatch.Stop();
            RenderTime = _stopwatch.ElapsedMilliseconds;
        }

        //绘制网格
        internal void DrawGrids()
        {
            SKColor GridColor = SKColors.LightGray;
            SKColor AxisColor = SKColors.DarkGray;
            using var gridPaint = new SKPaint
            {
                Color = GridColor,
                StrokeWidth = 1,
                IsAntialias = true,
                Style = SKPaintStyle.Stroke
            };

            using var axisPaint = new SKPaint
            {
                Color = AxisColor,
                StrokeWidth = 2,
                IsAntialias = true,
                Style = SKPaintStyle.Stroke
            };

            SKPaint pen = new SKPaint
            {
                Color = SKColors.DarkGray,         // 设置颜色
                StrokeWidth = 0.3f,              // 设置线宽
                Style = SKPaintStyle.Stroke   // 设置为仅绘制边框
            };

            for (int i = -500; i <= 500; i += Config.DocumentGridInterval)
            {
                Vector2 pt1 = new Vector2((float)i, 500f);
                Vector2 pt2 = new Vector2((float)i, -500f);
                DrawLine(pt1, pt2, gridPaint);

            }
            for (int j = -500; j <= 500; j += Config.DocumentGridInterval)
            {
                Vector2 pt1 = new Vector2(-500f, (float)j);
                Vector2 pt2 = new Vector2(500f, (float)j);
                DrawLine(pt1, pt2, gridPaint);
            }
        }

        //绘制坐标轴
        private void DrawAxes()
        {
            SKPaint pen = new SKPaint
            {
                Color = SKColors.DarkGray,         // 设置颜色
                StrokeWidth = 1f,              // 设置线宽
                Style = SKPaintStyle.Stroke   // 设置为仅绘制边框
            };

            Vector2 pt1 = new Vector2(-10000f, (float)0);
            Vector2 pt2 = new Vector2(10000f, (float)0);
            DrawLine(pt1, pt2, pen);

            Vector2 pt3 = new Vector2((float)0, 10000f);
            Vector2 pt4 = new Vector2((float)0, -10000f);
            DrawLine(pt3, pt4, pen);
        }

        //绘制鼠标操作
        private void DrawIndicator()
        {
            _pointer.OnPaint(this);
        }

        //绘制标尺Ruler
        private void DrawRuler()
        {

            float RulerThickness = 30; // 标尺厚度
            SKColor RulerBackgroundColor = SKColors.LightGray; // 标尺背景颜色
            SKColor TickColor = SKColors.Black; // 刻度颜色
            SKColor TextColor = SKColors.Black; // 文本颜色
            float MajorTickLength = 10; // 主刻度线长度
            float MinorTickLength = 5; // 次刻度线长度
            float TextMargin = 3; // 文本与刻度线的间距

            using var backgroundPaint = new SKPaint { Color = RulerBackgroundColor };
            using var tickPaint = new SKPaint { Color = TickColor, StrokeWidth = 1, IsAntialias = true };
            using var textPaint = new SKPaint
            {
                Color = TextColor,
                TextSize = 12,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial")
            };

            // 绘制标尺背景
            Canvas.DrawRect(0, 0, (float)Width, RulerThickness, backgroundPaint); // 顶部标尺
            Canvas.DrawRect(0, 0, RulerThickness, (float)Height, backgroundPaint); // 左侧标尺

            // 获取逻辑坐标范围
            Vector2 topLeft = CanvasToModel(new Vector2(0, 0));// new SKPoint(0, 0);
            Vector2 bottomRight = CanvasToModel(new Vector2((float)Width, (float)Height));

            // 动态调整刻度间距
            float scale = _matrixTrans.ScaleX;
            float majorTickSpacing = 50; // 主刻度间距
            float minorTickSpacing = majorTickSpacing / 5; // 次刻度间距



            // 绘制水平标尺（顶部）
            for (float x = (float)topLeft.X; x < bottomRight.X; x += majorTickSpacing)
            {
                float screenX = (float)x + RulerThickness; // 转换为屏幕坐标

                Canvas.DrawLine(screenX, RulerThickness - MajorTickLength, screenX, RulerThickness, tickPaint);

                // 绘制主刻度文字
                var text = $"{Math.Round(screenX, 2)}";
                var textBounds = new SKRect();
                textPaint.MeasureText(text, ref textBounds);
                Canvas.DrawText(text, (float)ModelToCanvas(screenX - textBounds.MidX), RulerThickness - MajorTickLength - TextMargin, textPaint);

                // 绘制次刻度
                //for (float minorX = x + minorTickSpacing; minorX < x + majorTickSpacing; minorX += minorTickSpacing)
                //{
                //    float screenMinorX = (float)(minorX )  + RulerThickness;
                //    Canvas.DrawLine(screenMinorX, RulerThickness - MinorTickLength, screenMinorX, RulerThickness, tickPaint);
                //}
            }

            // 绘制垂直标尺（左侧）
            //for (float y = (float)Math.Floor(topLeft.Y / majorTickSpacing) * majorTickSpacing; y < bottomRight.Y; y += majorTickSpacing)
            //{
            //    float screenY = (y - topLeft.Y) * scale + RulerThickness; // 转换为屏幕坐标
            //    Canvas.DrawLine(RulerThickness - MajorTickLength, screenY, RulerThickness, screenY, tickPaint);

            //    // 绘制主刻度文字
            //    var text = $"{Math.Round(y, 2)}";
            //    var textBounds = new SKRect();
            //    textPaint.MeasureText(text, ref textBounds);
            //    Canvas.DrawText(text, RulerThickness - MajorTickLength - textBounds.Width - TextMargin, screenY + textBounds.MidY, textPaint);

            //    // 绘制次刻度
            //    for (float minorY = y + minorTickSpacing; minorY < y + majorTickSpacing; minorY += minorTickSpacing)
            //    {
            //        float screenMinorY = (minorY - topLeft.Y) * scale + RulerThickness;
            //        Canvas.DrawLine(RulerThickness - MinorTickLength, screenMinorY, RulerThickness, screenMinorY, tickPaint);
            //    }
            //}


        }

        //绘制选择框
        private void DrawSelectedBBox()
        {
            this.SelectedBoundRect.Clear();
            if (Document.SelectedEntitys == null) return;

            foreach (EntityBase entity in Document.SelectedEntitys)
            {
                if (entity == null) continue;
                this.SelectedBoundRect.Union(entity.BoundingBox);
            }

            if (this.SelectedBoundRect.IsEmpty)
                return;
            this.SelectedBoundRect.Draw(this);
        }

        private void DrawWatermark(int width, int height)
        {

            string watermarkText = "MCT 铭创智能";
            var paint = new SKPaint
            {
                Color = SKColors.Gray.WithAlpha(20),
                TextSize = 40,
                IsAntialias = true,
                TextAlign = SKTextAlign.Center,
                Typeface = SKTypeface.FromFamilyName("微软雅黑", SKFontStyle.Bold)
            };


            var textBounds = new SKRect();
            paint.MeasureText(watermarkText, ref textBounds);
            float textWidth = textBounds.Width;
            float textHeight = textBounds.Height;

            float angle = -45;
            Canvas.Save();
            Canvas.RotateDegrees(angle, width / 2, height / 2);
            float stepX = textWidth * 2;
            float stepY = textHeight * 2;
            for (float y = -height; y < height * 2; y += stepY)
            {
                for (float x = -width; x < width * 2; x += stepX)
                {
                    Canvas.DrawText(watermarkText, x, y, paint);
                }
            }

            Canvas.Restore();
        }


        private void DrawLabel(int width, int height)
        {
            // 标签内容
            string labelText = "内测版本";

            // 标签尺寸和位置
            float padding = 15;
            float labelWidth = 120;  // 标签宽度
            float labelHeight = 35; // 标签高度
            float cornerRadius = 12; // 圆角半径
            float labelX = width - labelWidth - padding;
            float labelY = padding;

            // 添加阴影效果
            using (var shadowPaint = new SKPaint())
            {
                shadowPaint.IsAntialias = true;
                shadowPaint.Color = SKColors.Black.WithAlpha(50); // 半透明黑色
                shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, 4);

                // 绘制阴影
                var shadowRect = new SKRect(labelX + 2, labelY + 2, labelX + labelWidth + 2, labelY + labelHeight + 2);
                Canvas.DrawRoundRect(shadowRect, cornerRadius, cornerRadius, shadowPaint);
            }

            // 绘制圆角矩形标签背景
            var rect = new SKRect(labelX, labelY, labelX + labelWidth, labelY + labelHeight);

            using (var paint = new SKPaint())
            {
                paint.IsAntialias = true;
                paint.Style = SKPaintStyle.Fill;

                // 优化的渐变背景
                paint.Shader = SKShader.CreateLinearGradient(
                    new SKPoint(rect.Left, rect.Top),
                    new SKPoint(rect.Right, rect.Bottom),
                    new[] { SKColors.LightGoldenrodYellow, SKColors.Orange },
                    null,
                    SKShaderTileMode.Clamp
                );

                Canvas.DrawRoundRect(rect, cornerRadius, cornerRadius, paint);
            }

            // 绘制标签边框
            using (var borderPaint = new SKPaint())
            {
                borderPaint.IsAntialias = true;
                borderPaint.Style = SKPaintStyle.Stroke;
                borderPaint.StrokeWidth = 1.5f;
                borderPaint.Color = SKColors.SaddleBrown;

                Canvas.DrawRoundRect(rect, cornerRadius, cornerRadius, borderPaint);
            }

            // 绘制标签文字
            using (var textPaint = new SKPaint())
            {
                textPaint.IsAntialias = true;
                textPaint.TextSize = 16; // 更精致的字体大小
                textPaint.Color = SKColors.White;
                textPaint.TextAlign = SKTextAlign.Center;
                textPaint.Typeface = SKTypeface.FromFamilyName("Microsoft YaHei", SKFontStyle.Bold);

                // 文字位置居中
                float textX = rect.MidX;
                float textY = rect.MidY + (textPaint.TextSize / 3); // 修正垂直居中偏移
                Canvas.DrawText(labelText, textX, textY, textPaint);
            }
        }

        //private void DrawLabel(  int width, int height)
        //{
        //    // 标签内容
        //    string labelText = "内测版本";

        //    // 标签尺寸和位置
        //    float padding = 15;
        //    float labelWidth = 120;  // 更小的宽度
        //    float labelHeight = 35; // 更小的高度
        //    float cornerRadius = 12; // 更柔和的圆角
        //    float labelX = width - labelWidth - padding;
        //    float labelY = padding;

        //    // 添加阴影效果
        //    using (var shadowPaint = new SKPaint())
        //    {
        //        shadowPaint.IsAntialias = true;
        //        shadowPaint.Color = SKColors.Black.WithAlpha(50); // 半透明黑色
        //        shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, 4);

        //        // 绘制阴影
        //        var shadowRect = new SKRect(labelX + 2, labelY + 2, labelX + labelWidth + 2, labelY + labelHeight + 2);
        //        Canvas.DrawRoundRect(shadowRect, cornerRadius, cornerRadius, shadowPaint);
        //    }

        //    // 绘制圆角矩形标签背景
        //    var rect = new SKRect(labelX, labelY, labelX + labelWidth, labelY + labelHeight);

        //    using (var paint = new SKPaint())
        //    {
        //        paint.IsAntialias = true;
        //        paint.Style = SKPaintStyle.Fill;

        //        // 优化的渐变背景
        //        paint.Shader = SKShader.CreateLinearGradient(
        //            new SKPoint(rect.Left, rect.Top),
        //            new SKPoint(rect.Right, rect.Bottom),
        //            new[] { SKColors.Pink, SKColors.SkyBlue },
        //            null,
        //            SKShaderTileMode.Clamp
        //        );

        //        Canvas.DrawRoundRect(rect, cornerRadius, cornerRadius, paint);
        //    }

        //    // 绘制标签边框
        //    using (var borderPaint = new SKPaint())
        //    {
        //        borderPaint.IsAntialias = true;
        //        borderPaint.Style = SKPaintStyle.Stroke;
        //        borderPaint.StrokeWidth = 1.5f;
        //        borderPaint.Color = SKColors.DeepPink;

        //        Canvas.DrawRoundRect(rect, cornerRadius, cornerRadius, borderPaint);
        //    }

        //    // 绘制标签文字
        //    using (var textPaint = new SKPaint())
        //    {
        //        textPaint.IsAntialias = true;
        //        textPaint.TextSize = 16; // 更精致的字体大小
        //        textPaint.Color = SKColors.White;
        //        textPaint.TextAlign = SKTextAlign.Center;
        //        textPaint.Typeface = SKTypeface.FromFamilyName("Microsoft YaHei", SKFontStyle.Bold);

        //        // 文字位置居中
        //        float textX = rect.MidX;
        //        float textY = rect.MidY + (textPaint.TextSize / 3); // 修正垂直居中偏移
        //        Canvas.DrawText(labelText, textX, textY, textPaint);
        //    }
        //}


        public bool HitTest(System.Windows.Point mousePosition, out EntityBase hittedEntity)
        {
            foreach (EntityLayer entityLayer in ((DocumentBase)Document.Clone()).Layers)
            {
                foreach (EntityBase entity in entityLayer.Children)
                {
                    if (entity != null && entity.HitTest(this, mousePosition, out hittedEntity))
                    {
                        entity.IsSelected = true;
                        HittedEntity = hittedEntity;
                        return true;
                    }
                    else
                    {
                        entity.IsSelected = false;
                    }
                }
            }
            hittedEntity = null;
            return false;
        }

        public int HitTest(Point mouseDownPosition, Point mouseCurPosition, out List<EntityBase> hittedEntitys)
        {
            double downX = mouseDownPosition.X;
            double downY = mouseDownPosition.Y;
            double currentX = mouseCurPosition.X;
            double currentY = mouseCurPosition.Y;

            double left = downX < currentX ? downX : currentX;
            double right = downX > currentX ? downX : currentX;
            double bottom = downY > currentY ? downY : currentY;
            double top = downY < currentY ? downY : currentY;

            var pt1 = CanvasToModel(new Vector2(left, top));
            var pt2 = CanvasToModel(new Vector2(right, bottom));
            left = pt1.X;
            top = pt1.Y;
            right = pt2.X;
            bottom = pt2.Y;
            //right = CanvasToModel(right);
            //top = CanvasToModel(top);
            //bottom = CanvasToModel(bottom);


            hittedEntitys = new List<EntityBase>();

            foreach (EntityLayer entityLayer in Document.Layers)
            {
                foreach (EntityBase entity in entityLayer.Children)
                {
                    if (entity != null && entity.HitTest(left, top, right, bottom, 0.02f))
                    {
                        entity.IsSelected = true;
                        hittedEntitys.Add(entity);
                    }
                    else if (entity != null)
                    {
                        entity.IsSelected = false;
                    }
                }
            }
            return hittedEntitys.Count;
        }

        public void AppendEntity(EntityBase entity)
        {
            //Block modelSpace = Document.Database.BlockTable["ModelSpace"] as Block;
            //modelSpace.AppendEntity(entity);
        }

        public void RepaintCanvas(bool bufferBitmapToRedraw = false)
        {
            //this.OnRender();
            Viewer.InvalidateVisual();
        }

        public void OnZoomFit(BoundingBox br = null)
        {
            if (br == null)
            {
                br = new BoundingBox();
                foreach (EntityLayer layer in this.Document.Layers)
                    br.Union(layer.BoundingBox);
                if (br.IsEmpty)
                {
                    SetPart(new SKRect((float)-500, (float)500, (float)500, (float)-500), 100);
                }
                else
                {
                    SetPart(new SKRect((float)br.Left, (float)br.Top, (float)br.Right, (float)br.Bottom), 100);
                }
            }


        }

        private void OnCmdInputResurn(DynInputCtrl sender, DynInputResult result)
        {
            switch (result.status)
            {
                case DynInputStatus.OK:
                    {
                        DynInputResult<string> cmdInputRet = result as DynInputResult<string>;
                        Command cmd = _cmdsFactory.NewCommand(cmdInputRet.value.ToLower());
                        if (cmd != null)
                        {
                            this.OnCommand(cmd);
                        }
                    }
                    break;

                case DynInputStatus.Cancel:
                    break;

                case DynInputStatus.Error:
                    break;

                default:
                    break;
            }
        }

        public void OnCommand(ICommand cmd)
        {
            _cmdsMgr.DoCommand(cmd as Command);
        }

        public void OnCommandFinished(Command cmd)
        {
            this.RepaintCanvas(true);
        }

        public void OnCommandCanceled(Command cmd)
        {
            this.RepaintCanvas(false);
        }

        public void OnSelectionChanged()
        {
            _pointer.OnSelectionChanged();
            this.RepaintCanvas(true);
        }

        public void DrawArrow(Vector2 start, Vector2 end, int size)
        {
            float arrowHeadSize = size / Math.Abs(_matrixTrans.ScaleX);

            if (arrowHeadSize > 10) arrowHeadSize = 10;
            float arrowAngle = 30;

            // 计算方向向量
            var direction = new SKPoint((float)end.X - (float)start.X, (float)end.Y - (float)start.Y);
            var length = (float)Math.Sqrt(direction.X * direction.X + direction.Y * direction.Y);
            direction = new SKPoint(direction.X / length, direction.Y / length);

            // 箭头两侧点
            float angleRad = arrowAngle * (float)Math.PI / 180;
            //arrowHeadSize =Math.Abs( (view as ViewBase).ModelToCanvas(arrowHeadSize));
            var left = new SKPoint(
                (float)end.X - arrowHeadSize * (direction.X * (float)Math.Cos(angleRad) - direction.Y * (float)Math.Sin(angleRad)),
                (float)end.Y - arrowHeadSize * (direction.Y * (float)Math.Cos(angleRad) + direction.X * (float)Math.Sin(angleRad))
            );

            var right = new SKPoint(
                (float)end.X - arrowHeadSize * (direction.X * (float)Math.Cos(-angleRad) - direction.Y * (float)Math.Sin(-angleRad)),
                (float)end.Y - arrowHeadSize * (direction.Y * (float)Math.Cos(-angleRad) + direction.X * (float)Math.Sin(-angleRad))
            );

            var mEnd = ModelToCanvas(end);
            var mLeft = ModelToCanvas(new Vector2(left.X, left.Y));
            var mRight = ModelToCanvas(new Vector2(right.X, right.Y));


            var vertices = new[] { new SKPoint((float)mEnd.X, (float)mEnd.Y), new SKPoint((float)mLeft.X, (float)mLeft.Y), new SKPoint((float)mRight.X, (float)mRight.Y) };

            var colors = new[] { SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90) };

            var skVertices = SKVertices.CreateCopy(SKVertexMode.Triangles, vertices, colors);

            var paint = new SKPaint()
            {
                IsAntialias = true,
                Style = SKPaintStyle.Fill,
                //Color = SKColors.Black
            };

            Canvas?.DrawVertices(skVertices, SKBlendMode.SrcOver, paint);

        }

        public void DrawArcWithArrow(EntityArc arc, int size)
        {
            SKPoint center = new SKPoint((float)arc.Center.X, (float)arc.Center.Y);
            float radius = (float)arc.Radius;
            float startAngle = (float)arc.StartAngle;
            float sweepAngle = (float)arc.SweepAngle;

            // 箭头相关参数
            float baseArrowSize = size; // 箭头基础长度
            float arrowHeadSize = baseArrowSize / Math.Abs(_matrixTrans.ScaleX); // 动态调整箭头大小
            float arrowAngle = 30;
            if (arrowHeadSize > 4) arrowHeadSize = 4;
            // 计算结束点角度
            float endAngle = startAngle + sweepAngle;
            float endAngleRad = endAngle * (float)Math.PI / 180;

            // 计算圆弧结束点坐标
            SKPoint endPoint = new SKPoint(
                center.X + radius * (float)Math.Cos(endAngleRad),
                center.Y + radius * (float)Math.Sin(endAngleRad)
            );

            // 计算圆弧在结束点处的切线方向向量
            // 切线方向是垂直于半径的方向，顺时针为 (-dy, dx)，逆时针为 (dy, -dx)
            SKPoint tangentDirection;
            if (arc.IsCloseWise)
                tangentDirection = new SKPoint(-(float)Math.Sin(endAngleRad), (float)Math.Cos(endAngleRad));
            else tangentDirection = new SKPoint((float)Math.Sin(endAngleRad), -(float)Math.Cos(endAngleRad));


            // 调整为箭头长度的方向向量
            SKPoint arrowVector = new SKPoint(
                -tangentDirection.X * arrowHeadSize,
                -tangentDirection.Y * arrowHeadSize
            );

            // 计算箭头两侧点
            float angleRad = arrowAngle * (float)Math.PI / 180;
            SKPoint left = new SKPoint(
                endPoint.X + arrowVector.X * (float)Math.Cos(angleRad) - arrowVector.Y * (float)Math.Sin(angleRad),
                endPoint.Y + arrowVector.X * (float)Math.Sin(angleRad) + arrowVector.Y * (float)Math.Cos(angleRad)
            );

            SKPoint right = new SKPoint(
                endPoint.X + arrowVector.X * (float)Math.Cos(-angleRad) - arrowVector.Y * (float)Math.Sin(-angleRad),
                endPoint.Y + arrowVector.X * (float)Math.Sin(-angleRad) + arrowVector.Y * (float)Math.Cos(-angleRad)
            );

            var mEnd = ModelToCanvas(new Vector2(endPoint.X, endPoint.Y));
            var mLeft = ModelToCanvas(new Vector2(left.X, left.Y));
            var mRight = ModelToCanvas(new Vector2(right.X, right.Y));


            var vertices = new[] { new SKPoint((float)mEnd.X, (float)mEnd.Y), new SKPoint((float)mLeft.X, (float)mLeft.Y), new SKPoint((float)mRight.X, (float)mRight.Y) };



            var colors = new[] { SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90), SKColors.Red.WithAlpha(90) };
            var skVertices = SKVertices.CreateCopy(SKVertexMode.Triangles, vertices, colors);

            using (var arrowPaint = new SKPaint
            {
                //Color = SKColors.Black,
                IsAntialias = true,
                Style = SKPaintStyle.Fill
            })
            {
                Canvas.DrawVertices(skVertices, SKBlendMode.SrcOver, arrowPaint);
            }
        }

        public void DrawLine(Vector2 p1, Vector2 p2, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) pen = DefaultPen;
            if (csys == CSYS.Model)
            {
                Vector2 startInCanvas = ModelToCanvas(p1);
                Vector2 endInCanvas = ModelToCanvas(p2);
                if (Canvas.Handle == (IntPtr)0)
                {
                    return;
                }
                Canvas?.DrawLine((float)startInCanvas.X, (float)startInCanvas.Y, (float)endInCanvas.X, (float)endInCanvas.Y, pen);
            }
            else
            {
                Canvas?.DrawLine((float)p1.X, (float)p1.Y, (float)p2.X, (float)p2.Y, pen);
            }
        }

        public void DrawArc(Vector2 center, double radius, double startAngle, double endAngle, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (csys == CSYS.Model)
            {
                Vector2 centerInCanvas = ModelToCanvas(center);
                double radiusInCanvas = Math.Abs(ModelToCanvas(radius));
                double startAngleInCanvas = 360 - endAngle;
                double endAngleInCanvas = 360 - startAngle;

                if (endAngle < startAngle)
                    endAngle += 360;
                SKRect ovalRect = new SKRect((float)(centerInCanvas.X - radiusInCanvas), (float)(centerInCanvas.Y - radiusInCanvas), (float)(centerInCanvas.X + radiusInCanvas), (float)(centerInCanvas.Y + radiusInCanvas));
                Canvas?.DrawArc(ovalRect, (float)startAngleInCanvas, (float)(endAngleInCanvas - startAngleInCanvas), false, pen);

            }
            else
            {
                if (endAngle < startAngle)
                    endAngle += 360;
                SKRect ovalRect = new SKRect((float)(center.X - radius), (float)(center.Y + radius), (float)(center.X + radius), (float)(center.Y - radius));
                Canvas?.DrawArc(ovalRect, (float)startAngle, (float)(endAngle - startAngle), false, pen);
            }
        }

        public void DrawCircle(Vector2 center, double radius, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            if (csys == CSYS.Model)
            {
                Vector2 centerInCanvas = ModelToCanvas(center);
                float radiusInCanvas = Math.Abs(ModelToCanvas(radius));
                if (Canvas?.Handle == (IntPtr)0)
                {
                    return;
                }
                Canvas?.DrawCircle((float)centerInCanvas.X, (float)centerInCanvas.Y, radiusInCanvas, pen);
            }
            else
            {
                Canvas?.DrawCircle((float)center.X, (float)center.Y, (float)radius, pen);
            }
        }

        public void DrawCircle(SKCanvas canvas, SKPaint pen, Vector2 center, double radius, CSYS csys = CSYS.Model)
        {
            if (csys == CSYS.Model)
            {
                Vector2 centerInCanvas = ModelToCanvas(center);
                double radiusInCanvas = ModelToCanvas(radius);
                canvas.DrawCircle((float)(centerInCanvas.X - radiusInCanvas), (float)(centerInCanvas.Y - radiusInCanvas), (float)radiusInCanvas * 2, DefaultPen);
            }
            else
            {
                canvas.DrawCircle((float)(center.X - radius), (float)(center.Y - radius), (float)radius * 2, DefaultPen);
            }
        }

        public void DrawRectangle(Vector2 position, double width, double height, SKPaint pen, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            if (csys == CSYS.Model)
            {
                var end = ModelToCanvas(new Vector2(position.X + (float)width, position.Y + (float)height));
                Vector2 posInCanvas = ModelToCanvas(position);
                if (Canvas?.Handle == (IntPtr)0)
                {
                    return;
                }

                Canvas?.DrawRect((float)posInCanvas.X, (float)posInCanvas.Y, (float)(end.X - posInCanvas.X), -(float)(end.Y - posInCanvas.Y), pen);
            }
            else
            {
                Canvas?.DrawRect((float)position.X, (float)position.Y, (float)width, (float)height, pen);
            }
        }

        public void DrawRectangle(Vector2 position, double width, double height, CSYS csys = CSYS.Model)
        {
            SKPaint pen = new SKPaint()
            {
                Color = SKColors.Blue,
                StrokeWidth = 0.6f,
                Style = SKPaintStyle.Stroke,
                IsAntialias = true
            };
            if (csys == CSYS.Model)
            {
                double widthInCanvas = ModelToCanvas(width);
                double heightInCanvas = ModelToCanvas(height);
                var end = ModelToCanvas(new Vector2(position.X + (float)width, position.Y + (float)height));
                Vector2 posInCanvas = ModelToCanvas(position);
                //posInCanvas.Y -= heightInCanvas;
                if (Canvas.Handle == (IntPtr)0)
                {
                    return;
                }

                Canvas.DrawRect((float)posInCanvas.X, (float)posInCanvas.Y, (float)(end.X - posInCanvas.X), -(float)(end.Y - posInCanvas.Y), pen);
            }
            else
            {
                Canvas.DrawRect((float)position.X, (float)position.Y, (float)width, (float)height, pen);
            }
        }

        public void DrawPoint(Vector2 point, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            pen.Style = SKPaintStyle.Fill;
            if (csys == CSYS.Model)
            {
                Vector2 endInCanvas = ModelToCanvas(point);
                if (Canvas?.Handle == (IntPtr)0)
                {
                    return;
                }

                Canvas?.DrawCircle((float)endInCanvas.X, (float)endInCanvas.Y, 5, pen);
            }
            else
            {
                Canvas?.DrawCircle((float)point.X, (float)point.Y, 5, pen);
            }
        }

        public void DrawPoint(SKCanvas canvas, SKPaint pen, Vector2 point)
        {
            canvas.DrawPoint((float)point.X, (float)point.Y, pen);
        }

        public void DrawCross(Vector2 point, double length, SKPaint pen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) pen = DefaultPen;
            if (csys == CSYS.Model)
            {
                Vector2 horizontal1InCanvas = ModelToCanvas(new Vector2(point.X - length / 2, point.Y));
                Vector2 horizontal2InCanvas = ModelToCanvas(new Vector2(point.X + length / 2, point.Y));
                Vector2 vertical1InCanvas = ModelToCanvas(new Vector2(point.X, point.Y + length / 2));
                Vector2 vertical2InCanvas = ModelToCanvas(new Vector2(point.X, point.Y - length / 2));
                if (Canvas.Handle == (IntPtr)0)
                {
                    return;
                }
                Canvas?.DrawLine((float)horizontal1InCanvas.X, (float)horizontal1InCanvas.Y, (float)horizontal2InCanvas.X, (float)horizontal2InCanvas.Y, pen);
                Canvas?.DrawLine((float)vertical1InCanvas.X, (float)vertical1InCanvas.Y, (float)vertical2InCanvas.X, (float)vertical2InCanvas.Y, pen);
            }
            else
            {
                Vector2 horizontal1 = new Vector2(point.X - length / 2, point.Y);
                Vector2 horizontal2 = new Vector2(point.X + length / 2, point.Y);
                Vector2 vertical1 = new Vector2(point.X, point.Y + length / 2);
                Vector2 vertical2 = new Vector2(point.X, point.Y - length / 2);
                Canvas?.DrawLine((float)horizontal1.X, (float)horizontal1.Y, (float)horizontal2.X, (float)horizontal2.Y, pen);
                Canvas?.DrawLine((float)vertical1.X, (float)vertical1.Y, (float)vertical2.X, (float)vertical2.Y, pen);
            }
        }

        public void DrawText(string text, Vector2 point, SKPaint pen = null, SKPaint rectPen = null, CSYS csys = CSYS.Model)
        {
            if (pen == null) return;
            SKRect rect = new SKRect();
            pen.MeasureText(text, ref rect);
            if (csys == CSYS.Model)
            {
                Vector2 endInCanvas = ModelToCanvas(point);
                if (Canvas?.Handle == (IntPtr)0)
                {
                    return;
                }
                Canvas?.DrawRect((float)(rect.Left + endInCanvas.X - 5), (float)(endInCanvas.Y + rect.Top - 5), rect.Width + 10, rect.Height + 10, rectPen);
                Canvas?.DrawText(text, (float)endInCanvas.X, (float)endInCanvas.Y, pen);
            }
            else
            {
                Canvas?.DrawRect((float)(rect.Left + point.X), (float)(point.Y + rect.Top), rect.Width, rect.Height, rectPen);
                Canvas?.DrawText(text, (float)point.X, (float)point.Y, pen);
            }
        }


        public float ModelToCanvas(double value)
        {
            //return (float)value / _matrixTrans.ScaleX;
            return (_matrixTrans.MapPoint(0, (float)value).Y) - (_matrixTrans.MapPoint(0, 0).Y);
        }

        public Vector2 ModelToCanvas(Vector2 pointInModel)
        {

            var pt = _matrixTrans.MapPoint(new SKPoint((float)pointInModel.X, (float)pointInModel.Y));

            return new Vector2(pt.X, pt.Y);
        }

        public float CanvasToModel(double value)
        {
            return _matrixTrans.Invert().MapPoint((float)value, 0).X;
        }

        public Vector2 CanvasToModel(Vector2 pointInCanvas)
        {
            var pt = _matrixTrans.Invert().MapPoint((float)pointInCanvas.X, (float)pointInCanvas.Y);
            return new Vector2(pt.X, pt.Y);
        }

        public void Dispose()
        {
            //this.Dispose(true);
            GC.SuppressFinalize(this);
        }

    }
}
