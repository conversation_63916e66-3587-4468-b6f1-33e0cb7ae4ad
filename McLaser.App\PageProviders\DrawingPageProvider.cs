#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Windows;
using McLaser.Core.Navigation;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Container;
using McLaser.EditViewerSk.Views;


namespace McLaser.App.PageProviders
{
    /// <summary>
    /// 绘图页面提供者
    /// 负责注册SkEditor绘图功能页面
    /// </summary>
    [PageProvider("绘图页面提供者", 50)]
    public class DrawingPageProvider  
    {
        private readonly ILogger? _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        [ImportingConstructor]
        public DrawingPageProvider()
        {
            _logger = IoC.Get<ILogger>();
        }

        /// <summary>
        /// 提供者优先级
        /// </summary>
        public int Priority => 50;

        /// <summary>
        /// 提供者名称
        /// </summary>
        public string Name => "绘图页面提供者";

        /// <summary>
        /// 获取要注册的页面信息列表
        /// </summary>
        /// <returns>页面信息列表</returns>
        public IEnumerable<PageInfo> GetPages()
        {
            _logger?.LogInfo("DrawingPageProvider: 开始提供绘图页面...");

            var pages = new List<PageInfo>
            {
                new PageInfo
                {
                    Id = "drawing",
                    Title = "绘图",
                    Description = "SkEditor绘图功能",
                    Icon = "✏",
                    PageFactory = CreateDrawingPage,
                    IsSingleton = true,
                    Group = "调试",
                    Order = 10
                }
            };

            _logger?.LogInfo($"DrawingPageProvider: 提供了 {pages.Count} 个页面");
            return pages;
        }

        /// <summary>
        /// 创建绘图页面
        /// </summary>
        /// <returns>绘图页面实例</returns>
        private FrameworkElement CreateDrawingPage()
        {
            try
            {
                _logger?.LogInfo("正在创建SkEditor绘图页面...");
                //SkEditor sk = new SkEditor();
                //sk.Init();
                return CreateFallbackDrawingPage();

            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建SkEditor绘图页面失败: {ex.Message}");
                _logger?.LogError($"异常详情: {ex}");
                return CreateFallbackDrawingPage();
            }
        }

        /// <summary>
        /// 创建备用绘图页面
        /// </summary>
        /// <returns>备用绘图页面</returns>
        private FrameworkElement CreateFallbackDrawingPage()
        {
            try
            {
                var grid = new System.Windows.Controls.Grid
                {
                    Margin = new Thickness(20)
                };

                // 添加行定义
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = GridLength.Auto });

                // 标题
                var title = new System.Windows.Controls.TextBlock
                {
                    Text = "绘图功能",
                    FontSize = 24,
                    FontWeight = System.Windows.FontWeights.Bold,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20)
                };
                System.Windows.Controls.Grid.SetRow(title, 0);
                grid.Children.Add(title);

                // 主要内容区域
                var contentPanel = new System.Windows.Controls.StackPanel
                {
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center
                };

                var description = new System.Windows.Controls.TextBlock
                {
                    Text = "绘图功能模块\n\n注意：SkEditor控件加载失败，显示备用界面\n\n可能的原因：\n• McLaser.EditViewerSk依赖项缺失\n• SkEditor初始化失败\n• Marker实例创建失败\n\n请检查相关组件是否正确配置",
                    FontSize = 16,
                    TextAlignment = System.Windows.TextAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20)
                };
                contentPanel.Children.Add(description);

                // 按钮面板
                var buttonPanel = new System.Windows.Controls.StackPanel
                {
                    Orientation = System.Windows.Controls.Orientation.Horizontal,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center
                };

                var refreshButton = new System.Windows.Controls.Button
                {
                    Content = "重新加载",
                    Width = 100,
                    Height = 35,
                    Margin = new Thickness(5),
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 120, 215)),
                    Foreground = System.Windows.Media.Brushes.White,
                    BorderThickness = new Thickness(0)
                };

                var helpButton = new System.Windows.Controls.Button
                {
                    Content = "帮助",
                    Width = 100,
                    Height = 35,
                    Margin = new Thickness(5),
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(100, 100, 100)),
                    Foreground = System.Windows.Media.Brushes.White,
                    BorderThickness = new Thickness(0)
                };

                buttonPanel.Children.Add(refreshButton);
                buttonPanel.Children.Add(helpButton);
                contentPanel.Children.Add(buttonPanel);

                System.Windows.Controls.Grid.SetRow(contentPanel, 1);
                grid.Children.Add(contentPanel);

                // 状态栏
                var statusText = new System.Windows.Controls.TextBlock
                {
                    Text = "绘图功能加载失败（备用模式）",
                    FontSize = 12,
                    Foreground = System.Windows.Media.Brushes.Red,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Margin = new Thickness(0, 10, 0, 0)
                };
                System.Windows.Controls.Grid.SetRow(statusText, 2);
                grid.Children.Add(statusText);

                _logger?.LogInfo("备用绘图页面创建成功");
                return grid;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建备用绘图页面失败: {ex.Message}");
                
                // 最后的备用方案
                return new System.Windows.Controls.TextBlock
                {
                    Text = $"绘图页面加载失败: {ex.Message}",
                    FontSize = 16,
                    Foreground = System.Windows.Media.Brushes.Red,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center,
                    Margin = new Thickness(20)
                };
            }
        }
    }
}
