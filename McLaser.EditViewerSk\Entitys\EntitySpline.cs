﻿using McLaser.EditViewerSk.Base;
using System.Collections.Generic;
using System.ComponentModel;
using System.Numerics;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntitySpline : EntityBase
    {

        private bool isClosed;


        [Browsable(true),ReadOnly(false),Category("Data"), DisplayName("Closed"), RefreshProperties(RefreshProperties.All)]
        public bool IsClosed
        {
            get => this.isClosed;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.isClosed = value;
                this.IsNeedToRegen = true;
            }
        }

    }
}
