﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Convertors
{
    public class BooleanTypeDescriptionProvider : TypeDescriptionProvider
    {
        private static readonly TypeDescriptionProvider DefaultProvider = TypeDescriptor.GetProvider(typeof(bool));

        public BooleanTypeDescriptionProvider() : base(DefaultProvider) { }

        public override ICustomTypeDescriptor GetTypeDescriptor(Type objectType, object instance)
        {
            var descriptor = base.GetTypeDescriptor(objectType, instance);
            return new BooleanTypeDescriptor(descriptor);
        }

        public static void Register()
        {
            TypeDescriptor.AddProvider(new BooleanTypeDescriptionProvider(), typeof(bool));
        }
    }
}
