﻿using McLaser.EditViewerSk.Dxf;
using System;
using System.ComponentModel;
using System.Globalization;


namespace McLaser.EditViewerSk.Convertors
{
    public class Vector2Converter : TypeConverter
    {
        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
        {
            return sourceType == typeof(string) || base.CanConvertFrom(context, sourceType);
        }

        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
        {
            try
            {
                if (value is string str)
                {
                    // 输入格式示例："1.2345, 5.6789"
                    string[] parts = str.Split(new[] { ',', ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length == 2 &&
                        double.TryParse(parts[0], NumberStyles.Float, culture, out double x) &&
                        double.TryParse(parts[1], NumberStyles.Float, culture, out double y))
                    {
                        return new Vector2(x, y);
                    }
                }

                return base.ConvertFrom(context, culture, value);
            }
            catch (Exception)
            {
                return new Vector2(0, 0);
            }
        }

        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            if (destinationType == typeof(string) && value is Vector2 vector)
            {
                return $"{vector.X:F4}, {vector.Y:F4}"; // 显示 4 位小数
            }
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }

}
