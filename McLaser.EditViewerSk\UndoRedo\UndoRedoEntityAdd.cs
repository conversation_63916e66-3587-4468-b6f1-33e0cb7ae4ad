﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.IO;

namespace McLaser.EditViewerSk.UndoRedo
{
    internal class UndoRedoEntityAdd : UndoRedoSingle
    {
        private DocumentBase doc;
        private EntityLayer layer;
        private List<EntityBase> entities;
        private int index = 0;

        public override void Execute() => this.Redo();

        public override void Undo()
        {
            for (int i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];
                if (entity is EntityLayer)
                    this.doc.Layers.Remove(entity as EntityLayer);
                else
                    (entity.Parent as EntityLayer)?.Remove(entity);
            }
        }

        public override void Redo()
        {
            for (int i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];
                if (entity is EntityLayer)
                {
                    this.doc.Layers.Add(entity as EntityLayer);
                    //this.entity.Index = this.doc.Layers.IndexOf(this.layer);
                }
                //else if (this.layer != null)
                //{
                //    entity.Parent = this.layer;
                //    this.layer.Add(entity);
                //    //this.entity.Index = this.doc.Layers.IndexOf(this.layer);
                //}
                else
                {
                    if (this.doc.ActiveLayer == null)
                        return;
                    entity.Parent = (EntityBase)this.doc.ActiveLayer;
                    if (index != -1)
                    {
                        this.doc.ActiveLayer.Insert(index, entity);
                    }
                    else
                        this.doc.ActiveLayer.Add(entity);
                    //this.entity.Index = this.doc.ActiveLayer.Children.IndexOf(this.entity);
                }
            }
        }

        public UndoRedoEntityAdd(DocumentBase doc, EntityLayer layer, List<EntityBase> entities, int index = -1)
        {
            this.Name = "Entity Add";
            this.doc = doc;
            this.layer = layer;
            this.entities = entities;
            this.index = index;
        }
    }
}
