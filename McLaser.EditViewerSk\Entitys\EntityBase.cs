﻿using McLaser.EditViewerSk.Interfaces;
using System.Collections.Generic;
using McLaser.EditViewerSk.Common;
using System.ComponentModel;
using System.Windows;
using SkiaSharp;
using System.Collections.ObjectModel;
using McLaser.EditViewerSk.Marker;
using PropertyTools;
using DragDropKeyStates = PropertyTools.DragDropKeyStates;
using System;
using McLaser.EditViewerSk.Base;
using Newtonsoft.Json;
using System.Windows.Media;
using System.Linq;
using SkiaSharp.Views.Desktop;
using McLaser.Entities;
using Point = System.Windows.Point;

namespace McLaser.EditViewerSk.Entitys
{
    public abstract class EntityBase :  IMarkerable, IRenderable, INotifyPropertyChanged, IDragSource, IDropTarget, ICloneable, IDisposable
    {

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }



        //public static readonly DependencyProperty IconProperty =
        //DependencyProperty.Register(
        //    nameof(Icon),
        //    typeof(ImageSource),
        //    typeof(EntityBase),
        //    new PropertyMetadata(null, null));

        //public ImageSource Icon
        //{
        //    get => (ImageSource)GetValue(IconProperty);
        //    set => SetValue(IconProperty, value);
        //}

        [JsonIgnore, Browsable(false)]
        public virtual object SyncRoot { get; protected set; }


        [JsonIgnore,Browsable(false), ReadOnly(false)]
        public virtual ulong Id { get; private set; }


        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual string TypeName => base.GetType().Name;

 
        [Browsable(false),ReadOnly(false)]
        public virtual SKColor Color
        {
            get
            {
                return this.color;
            }
            set
            {
                if (!Config.PenColors.Contains(value))
                {
                    return;
                }
                this.color = value;
            }
        }


        [ Browsable(true), ReadOnly(false)]
        public string Name { get { return name; } set { name = value; OnPropertyChanged("Name"); } }

        [JsonIgnore, Browsable(false)]
        private ImageSource _icon;
        [JsonIgnore, Browsable(false)]
        public ImageSource Icon
        {
            get { return _icon; }
            set { _icon = value; OnPropertyChanged("Icon"); }
        }


        [JsonIgnore, Browsable(true), ReadOnly(true)]
        public virtual string Description { get { return this.description; } set { this.description = value; } }


        [Browsable(false)]
        public bool IsSelected { get { return isSelected; } set { isSelected = value; OnPropertyChanged("IsSelected"); } }
        [Browsable(false)]
        public bool IsExpanded { get { return isIsExpanded; } set { isIsExpanded = value; OnPropertyChanged("IsExpanded"); } }
        [Browsable(false)]
        public bool IsRenderable { get; set; } = true;
        [Browsable(false)]
        public bool IsNeedToRegen { get; set; } = false;
        [Browsable(false)]
        public bool IsMarkerable { get; set; } = true;
        [Browsable(false)]
        public bool IsReversable { get; set; } = false;
        [Browsable(false)]
        public virtual bool IsVisible { get; set; }
        [Browsable(false)]
        public bool IsLocked { get; set; } = false;

        public int Index { get { return index; } set { index = value; } }

        [JsonIgnore, Browsable(false)]
        public bool IsNewStroke { get; set; } = true;


        [JsonIgnore, Browsable(false)]
        public virtual bool IsHasChildren => this.Children.Count > 0;

        [TypeConverter(typeof(ExpandableObjectConverter))]
        [DisplayName("加工参数")]
        public virtual MarkArg Arg { get; set; } = new MarkArg();


        [JsonIgnore, Browsable(false)]
        public virtual EntityBase Parent { get; set; }

        [Browsable(false)]
        public virtual BoundingBox BoundingBox { get; set; }  = BoundingBox.Empty;

        [Browsable(false)]
        public SKPaint Pen = new SKPaint()
        {
            Color = SKColors.Black,
            StrokeWidth = 1f,
            Style = SKPaintStyle.Stroke,
            IsAntialias = true
        };


        [Browsable(false)]
        public ObservableCollection<EntityBase> Children { get; set; } = new ObservableCollection<EntityBase>();

        public ObservableCollection<EntityObject> DxfEntitys { get; set; } = new ObservableCollection<EntityObject>();


        [JsonIgnore, Browsable(false)]
        public virtual bool IsDisposed => disposed;


        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual int ChildCount => Children.Count;


        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual Vector2 In { get; protected set; }


        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual Vector2 Out { get; protected set; }


        [Browsable(false), ReadOnly(false), TypeConverter(typeof(ExpandableObjectConverter))]
        public virtual BoundingBox BBox { get; set; }



        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Angle")]
        public virtual double Angle
        {
            get => this.angle;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                angle = value;
            }
        }






        [Browsable(false),ReadOnly(false)]
        public virtual Alignments Alignment
        {
            get
            {
                return this.alignment;
            }
            set
            {
                this.alignment = value;
                if (IsLocked)
                {
                    return;
                }
                BoundingBox bbox = this.BBox;
                if (bbox != null)
                {
                    bbox.Regen();
                }
                this.RegenInOut();
                this.RegenAlign();
            }
        }


        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual Vector2 ModelAlign
        {
            get
            {
                return this.modelAlign;
            }
            set
            {
                this.modelAlign = value;
                if (IsLocked)
                {
                    return;
                }
                BoundingBox bbox = this.BBox;
                if (bbox != null)
                {
                    bbox.Regen();
                }
                this.RegenInOut();
            }
        }


        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual Vector2 ModelScale
        {
            get
            {
                return modelScale;
            }
            set
            {
                if (value.X <= 0f || value.Y <= 0f)
                {
                    return;
                }
                modelScale = value;
                if (IsLocked)
                {
                    return;
                }
                BoundingBox bbox = this.BBox;
                if (bbox != null)
                {
                    bbox.Regen();
                }
                this.RegenInOut();
            }
        }

        [JsonIgnore, Browsable(false), ReadOnly(false)]
        public virtual Vector2 ModelRotate
        {
            get
            {
                return modelRotate;
            }
            set
            {
                modelRotate = value;
                if (IsLocked)
                {
                    return;
                }
                BoundingBox bbox = this.BBox;
                if (bbox != null)
                {
                    bbox.Regen();
                }
                this.RegenInOut();
            }
        }

        [JsonIgnore, Browsable(false),ReadOnly(false)]
        public virtual Vector2 ModelTranslate
        {
            get
            {
                return this.modelTranslate;
            }
            set
            {
                this.modelTranslate = value;
                if (IsLocked)
                {
                    return;
                }
                BoundingBox bbox = this.BBox;
                if (bbox != null)
                {
                    bbox.Regen();
                }
                this.RegenInOut();
            }
        }

        [JsonIgnore, Browsable(false)]
        public virtual SKMatrix ModelMatrix
        {
            get
            {
                SKMatrix left = SKMatrix.Identity;
                Alignments alignments = this.Alignment;
                if (alignments != Alignments.None)
                {
                    if (alignments == Alignments.Custom)
                    {
                        left = SKMatrix.CreateTranslation((float)ModelAlign.X, (float)ModelAlign.Y);
                    }
                    else
                    {
                        left = SKMatrix.CreateTranslation((float)(-BBox.Min.X + ModelAlign.X), (float)(-BBox.Min.Y + ModelAlign.Y));
                    }
                }
                SKMatrix scale = SKMatrix.CreateScale((float)ModelScale.X, (float)ModelScale.Y);
                SKMatrix rotateX = SKMatrix.CreateRotationDegrees((float)ModelRotate.X);
                SKMatrix translate = SKMatrix.CreateTranslation((float)ModelTranslate.X, (float)ModelTranslate.Y);
                SKMatrix matrix = left.PostConcat(scale).PostConcat(rotateX).PostConcat(translate);
                if (this.Parent != null)
                {
                    matrix = matrix.PostConcat(Parent.ModelMatrix);
                }
                return matrix;
            }
        }


        [Browsable(false), ReadOnly(false)]
        public virtual int Repeats { get { return repeats; } set { repeats = value; } }


        [Browsable(false), ReadOnly(false)]
        public virtual bool IsHitTestable { get; set; }


        [JsonIgnore, Browsable(false)]
        public virtual IView View { get; protected set; }

        public override string ToString()
        {
            return this.Name ?? "";
        }

        public virtual object Clone()
        {
            return this.Clone();    
        }


        [JsonIgnore, Browsable(false)]
        public virtual object Tag { get; set; }

        protected virtual EntityBase InternalClone(EntityBase entity)
        {
            this.Color = entity.Color;
            this.Name = entity.Name;
            this.Description = entity.Description;
            this.Parent = entity.Parent;
            this.IsRenderable = entity.IsRenderable;
            this.IsMarkerable = entity.IsMarkerable;
            this.IsHitTestable = entity.IsHitTestable;
            this.IsSelected = entity.IsSelected;
            this.BBox = (BoundingBox)entity.BBox.Clone();
            this.alignment = entity.Alignment;
            this.modelAlign = entity.ModelAlign;
            this.modelTranslate = entity.ModelTranslate;
            this.modelScale = entity.ModelScale;
            this.modelRotate = entity.ModelRotate;
            this.repeats = entity.Repeats;
            if (entity.Tag != null)
            {
                ICloneable cloneable = entity.Tag as ICloneable;
                if (cloneable != null)
                {
                    this.Tag = cloneable.Clone();
                    return entity;
                }
            }
            this.Tag = entity.Tag;
            return entity;
        }

        //字段
        protected SKColor color;
        protected string name = "Entity";
        protected string description;
        private bool isSelected = true;
        private bool isIsExpanded = false;
        private int index = -1;
        protected Alignments alignment;
        private double angle;
        protected Vector2 modelAlign;
        protected Vector2 modelScale;
        protected Vector2 modelRotate;
        protected Vector2 modelTranslate;
        private bool disposed;
        internal static ulong Count = 1;
        protected int repeats = 1;
        public EntityBase()
        {
            EntityBase.Count += 1;
            this.SyncRoot = new object();
        
            this.Children = new ObservableCollection<EntityBase>();
            this.modelScale = Vector2.One;
            this.modelAlign = Vector2.Zero;
            this.ModelRotate = Vector2.Zero;
            this.modelTranslate = Vector2.Zero;
      
            this.Color = Config.ViewDefaultEntityColor;
            this.BBox = BoundingBox.Empty;
            this.BBox.Parent = this;
            this.alignment = Alignments.None;
            this.In = Vector2.Zero;
            this.Out = Vector2.Zero;
            this.Repeats = 1;
            this.IsRenderable = true;
            this.IsMarkerable = true;
            this.IsHitTestable = true;
            this.IsSelected = false;
            this.IsNeedToRegen = true;
        }



        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    if (Children != null)
                    {
                        foreach (EntityBase entity in Children)
                        {
                            IDisposable disposable = entity as IDisposable;
                            if (disposable != null && disposable != null)
                            {
                                disposable.Dispose();
                            }
                        }
                        Children = null;
                    }
                }
                disposed = true;
            }
        }

        public bool CanDrop(IDragSource node, DropPosition mode, DragDropEffect effect)
        {
  
            return node is EntityBase && (mode == DropPosition.Add || this.Parent != null);
        }

        public void Drop(IEnumerable<IDragSource> nodes, DropPosition mode, DragDropEffect effect, DragDropKeyStates initialKeyStates)
        {
            foreach (var node in nodes)
            {
                this.Drop(node, mode, effect == DragDropEffect.Copy);
            }
        }

        public void Drop(IDragSource node, DropPosition mode, bool copy)
        {
            var cvm = node as EntityBase;
            if (copy) cvm = (EntityBase)cvm.Clone();
            if (mode == DropPosition.Add) mode = DropPosition.InsertAfter;
            switch (mode)
            {
                case DropPosition.Add:
                    //this.Children.Add(cvm);
                    //cvm.Parent = this;
                    this.IsExpanded = true;
                    break;
                case DropPosition.InsertBefore:
                
                    int index = this.Parent.Children.IndexOf(this);
                    ((EntityLayer)Parent).Insert(index, cvm);
                 
                    //Parent.Children.Insert(index, cvm);
                    cvm.Parent = this.Parent;
                    break;
                case DropPosition.InsertAfter:
                  
                    int index2 = this.Parent.Children.IndexOf(this);
                    ((EntityLayer)Parent).Insert(index2+1, cvm);
                
                    //Parent.Children.Insert(index2 + 1, cvm);
                    cvm.Parent = this.Parent;
                    break;
            }
        }

        [JsonIgnore, Browsable(false)]
        public bool IsDraggable => Parent != null;

        public void Detach()
        {
            this.Parent.Children.Remove(this);
            this.Parent = null;
        }

        public virtual bool Mark(IMarker marker)
        {
            return true;
        }

        public virtual bool Mark(IMarkerArg markerArg, IMarkCommand cmd)
        {

            return true;
        }

        public virtual bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return false;
        }

        public virtual bool HitTest(double x, double y, double threshold = 0.02)
        {
            return false;
        }

        public virtual bool HitTest(BoundingBox rect, double threshold = 0.02)
        {
            return false;
        }

        public virtual bool HitTest(IView view, Point mousePosition, out EntityBase hittedEntity)
        {
            hittedEntity = null;
            bool isHitTestable = this.IsHitTestable;
            return false;
        }

        public virtual bool HitTest(IView view, Vector2 start, Vector2 end, out EntityBase hittedEntity)
        {
            hittedEntity = null;
            return this.IsHitTestable && this.BBox.HitTest(view, start, end);
        }

        public virtual bool IsEqual(SKColor color)
        {
            return true;
            //return this.Color.ToDrawingColor().ToArgb() == color.ToDrawingColor().ToArgb();
        }

        //重构
        public virtual void Regen()
        {
           
        }

        public virtual void RegenInOut()
        {
            foreach (EntityBase entity in this.Children)
            {
                entity.RegenInOut();
            }
        }

        public virtual void RegenAlign()
        {
            if (this.BBox.IsEmpty)
            {
                return;
            }
            Vector2 vector = this.BBox.Max - this.BBox.Min;
            switch (this.alignment)
            {
                case Alignments.None:
                case Alignments.Custom:
                    break;
                case Alignments.TopLeft:
                    this.ModelAlign = new Vector2(0f, -vector.Y);
                    return;
                case Alignments.TopCenter:
                    this.ModelAlign = new Vector2(-vector.X / 2f, -vector.Y);
                    return;
                case Alignments.TopRight:
                    this.ModelAlign = new Vector2(-vector.X, -vector.Y);
                    return;
                case Alignments.MiddleLeft:
                    this.ModelAlign = new Vector2(0f, -vector.Y / 2f);
                    return;
                case Alignments.MiddleCenter:
                    this.ModelAlign = new Vector2(-vector.X / 2f, -vector.Y / 2f);
                    return;
                case Alignments.MiddleRight:
                    this.ModelAlign = new Vector2(-vector.X, -vector.Y / 2f);
                    return;
                case Alignments.BottomLeft:
                    this.ModelAlign = Vector2.Zero;
                    return;
                case Alignments.BottomCenter:
                    this.ModelAlign = new Vector2(-vector.X / 2f, 0f);
                    return;
                case Alignments.BottomRight:
                    this.ModelAlign = new Vector2(-vector.X, 0f);
                    break;
                default:
                    return;
            }
        }

        public virtual void Translate(double dx, double dy)
        {
            this.ModelTranslate += new Vector2(dx, dy);
            BoundingBox bbox = this.BBox;
            if (bbox == null)
            {
                return;
            }
            bbox.Regen();
        }

        public virtual void Translate(Vector2 deltaXy)
        {
            this.ModelTranslate += deltaXy;
            BoundingBox bbox = this.BBox;
            if (bbox == null)
            {
                return;
            }
            bbox.Regen();
        }

        public virtual void RotateX(double dAngle)
        {
            this.ModelRotate += new Vector2((float)dAngle, 0f);
            BoundingBox bbox = this.BBox;
            if (bbox == null)
            {
                return;
            }
            bbox.Regen();
        }

        public virtual void RotateY(double dAngle)
        {
            this.ModelRotate += new Vector2(0f, (float)dAngle);
            BoundingBox bbox = this.BBox;
            if (bbox == null)
            {
                return;
            }
            bbox.Regen();
        }

        public virtual void Rotate(double dAngleX, double dAngleY)
        {
            this.ModelRotate += new Vector2(dAngleX, dAngleY);
            BoundingBox bbox = this.BBox;
            if (bbox == null)
            {
                return;
            }
            bbox.Regen();
        }

        public virtual void Rotate(Vector2 deltaAngle)
        {
            this.Rotate(deltaAngle.X, deltaAngle.Y);
        }

      

        public virtual void Scale(double dScaleX, double dScaleY)
        {
            this.ModelScale *= new Vector2(dScaleX, dScaleY);
            BoundingBox bbox = this.BBox;
            if (bbox == null)
            {
                return;
            }
            bbox.Regen();
        }

        public virtual void Scale(Vector2 deltaScale)
        {
            this.Scale(deltaScale.X, deltaScale.Y);
        }


        
      

        //旋转
        public virtual void Rotate(double angle)
        {

        }

        //旋转
        public virtual void Rotate(double angle, Vector2 rotateCenter)
        {

        }

      
       

        //缩放
        public virtual void Scale(Vector2 scale, Vector2 scaleCenter)
        {

        }


        //渲染
        public virtual void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            this.View = view;
            foreach (EntityBase entity in Children)
            {
                entity?.Render(view);
            }
        }




  











        /// <summary>
        /// 对象捕捉
        /// </summary>
        public virtual List<ObjectSnapPoint> GetSnapPoints()
        {
            return null;
        }

        /// <summary>
        /// 获取夹点
        /// </summary>
        public virtual List<GripPoint> GetGripPoints()
        {
            return null;
        }

        /// <summary>
        /// 设置夹点
        /// </summary>
        public virtual void SetGripPointAt(int index, GripPoint gripPoint, Vector2 newPosition)
        {
        }





    }


}
