﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.Common
{
    /// <summary>
    /// 捕捉节点管理器
    /// </summary>
    public class MgrSnap
    {
        private ViewBase _viewer = null;

        private ObjectSnapPoint _currObjectSnapPoint = null;
        public ObjectSnapPoint currentObjectSnapPoint
        {
            get { return _currObjectSnapPoint; }
        }

        public MgrSnap(ViewBase viewer)
        {
            _viewer = viewer;
        }

        public Vector2 Snap(float x, float y)
        {
            return this.Snap(new Vector2(x, y));
        }

 

        public Vector2 Snap(Vector2 posInCanvas)
        {
            Vector2 posInModel = _viewer.CanvasToModel(posInCanvas);
            var cmd = _viewer._cmdsMgr.CurrentCmd;

            if (cmd != null)
            {
                if (cmd is LineCmd lineCmd)
                {
                    if (TrySnapToEntities(lineCmd.CurLine?.StartPoint ?? Vector2.Zero, posInModel))
                    {
                        return _currObjectSnapPoint.position;
                    }
                }
                else if (cmd is PolylineCmd polylineCmd)
                {
                    if (TrySnapToEntities(polylineCmd.CurLine?.StartPoint ?? Vector2.Zero, posInModel))
                    {
                        return _currObjectSnapPoint.position;
                    }
                }
                else
                {
                    if (TrySnapToEntityPoints(posInModel))
                    {
                        return _currObjectSnapPoint.position;
                    }
                }
            }

            return SnapToGrid(posInModel);
        }

        // 处理直线和圆的切点，以及相关的snap点
        private bool TrySnapToEntities(Vector2 startPoint, Vector2 posInModel)
        {
            foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
            {
                // 处理圆形实体  
                if (entity is EntityCircle circle && startPoint != Vector2.Zero)
                {
                    if (CalculateTangentPoints(startPoint, circle.Center, circle.Radius, out List<Vector2> tangentPoints))
                    {
                        foreach (var point in tangentPoints)
                        {
                            if (IsWithinThreshold(point, posInModel))
                            {
                                _currObjectSnapPoint = new ObjectSnapPoint(ObjectSnapMode.Tangent, point);
                                return true;
                            }
                        }
                    }
                }

                // 处理其他点  
                var snapPoints = entity.GetSnapPoints();
                if (snapPoints != null && snapPoints.Count > 0)
                {
                    foreach (ObjectSnapPoint snapPnt in snapPoints)
                    {
                        if (IsWithinThreshold(snapPnt.position, posInModel))
                        {
                            _currObjectSnapPoint = snapPnt;
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        // 处理snap点
        private bool TrySnapToEntityPoints(Vector2 posInModel)
        {
            foreach (EntityBase entity in _viewer.Document.ActiveLayer.Children)
            {
                var snapPoints = entity.GetSnapPoints();
                if (snapPoints != null && snapPoints.Count > 0)
                {
                    foreach (ObjectSnapPoint snapPnt in snapPoints)
                    {
                        if (IsWithinThreshold(snapPnt.position, posInModel))
                        {
                            _currObjectSnapPoint = snapPnt;
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        private bool IsWithinThreshold(Vector2 point, Vector2 posInModel)
        {
            double distance = (point - posInModel).Length;
            return Math.Abs(_viewer.ModelToCanvas(distance)) <= _threshold;
        }

        private Vector2 SnapToGrid(Vector2 posInModel)
        {
            int index = _viewer.GridSnaps.FindIndex(item => Math.Abs(_viewer.ModelToCanvas((item.position - posInModel).Length)) <= _threshold);
            if (index != -1)
            {
                _currObjectSnapPoint = _viewer.GridSnaps[index];
                return _currObjectSnapPoint.position;
            }

            _currObjectSnapPoint = null;
            return posInModel;
        }

        public bool CalculateTangentPoints(Vector2 externalPoint, Vector2 centerPoint, double radius, out List<Vector2> tangentPoints)
        {
            tangentPoints = new List<Vector2>();

            // 1. 计算圆外一点到圆心的距离
            double distance = Math.Sqrt(Math.Pow(externalPoint.X - centerPoint.X, 2) + Math.Pow(externalPoint.Y - centerPoint.Y, 2));

            // 2. 处理点在圆内或圆上的情况
            if (distance <= radius)
            {
                return false; // 返回空数组，表示不存在切点
            }

            // 点p 到切点的距离
            double length = Math.Sqrt(distance * distance - radius * radius);

            // 点到圆心的单位向量
            Vector2 unit = new Vector2((centerPoint.X - externalPoint.X) / distance, (centerPoint.Y - externalPoint.Y) / distance);

            // 计算切线与点心连线的夹角
            double angle = Math.Asin(radius / distance);

            // 向正反两个方向旋转单位向量
            Vector2 point1 = new Vector2();
            Vector2 point2 = new Vector2();
            point1.X = unit.X * Math.Cos(angle) - unit.Y * Math.Sin(angle);
            point1.Y = unit.X * Math.Sin(angle) + unit.Y * Math.Cos(angle);
            point2.X = unit.X * Math.Cos(-angle) - unit.Y * Math.Sin(-angle);
            point2.Y = unit.X * Math.Sin(-angle) + unit.Y * Math.Cos(-angle);
            // 得到新座标
            point1.X = point1.X * length + externalPoint.X;
            point1.Y = point1.Y * length + externalPoint.Y;
            point2.X = point2.X * length + externalPoint.X;
            point2.Y = point2.Y * length + externalPoint.Y;

            tangentPoints.Add(point1);
            tangentPoints.Add(point2);

            //Action.ActEntityAdd(new EntityPoint(tangentPoints[0]));
            //Action.ActEntityAdd(new EntityPoint(tangentPoints[1]));

            return true;
        }

        public void Clear()
        {
            _currObjectSnapPoint = null;
        }

        private double _threshold = 8;
        public void OnPaint(ViewBase viewer)
        {
            if (_currObjectSnapPoint != null)
            {
                SKPaint pen = new SKPaint()
                {
                    Color = SKColors.DeepPink,
                    StrokeWidth = 2f,
                    Style = SKPaintStyle.Stroke,
                    IsAntialias = true,
                };

                SKPaint pen1 = new SKPaint()
                {
                    TextSize = 16,
                    Typeface = SKTypeface.FromFamilyName("微软雅黑"),
                    Color = SKColors.Black,
                    StrokeWidth = 2f,
                    Style = SKPaintStyle.Fill,
                    IsAntialias = true,
                };

                SKPaint rectPen = new SKPaint()
                {
                    Color = SKColors.Gold,
                    IsStroke = true,
                    StrokeWidth = 2f,
                    Style = SKPaintStyle.StrokeAndFill,
                    IsAntialias = true,
                };

                switch (_currObjectSnapPoint.type)
                {
                    case ObjectSnapMode.End:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("端点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;

                    case ObjectSnapMode.Mid:
                        {
                            Vector2 offset = new Vector2(0, (float)(_threshold * 0.6));
                            Vector2 point1 = _currObjectSnapPoint.position + offset;

                            offset = Rotate(offset, 120);
                            Vector2 point2 = _currObjectSnapPoint.position + offset;
                            offset = Rotate(offset, 120);
                            Vector2 point3 = _currObjectSnapPoint.position + offset;

                            _viewer.DrawLine(point1, point2, pen);
                            _viewer.DrawLine(point2, point3, pen);
                            _viewer.DrawLine(point3, point1, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("中点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;

                    case ObjectSnapMode.Center:
                        {
                            Vector2 offset = new Vector2(0, (float)(_threshold * 0.6));
                            Vector2 point1 = _currObjectSnapPoint.position + offset;

                            offset = Rotate(offset, 120);
                            Vector2 point2 = _currObjectSnapPoint.position + offset;
                            offset = Rotate(offset, 120);
                            Vector2 point3 = _currObjectSnapPoint.position + offset;

                            _viewer.DrawLine(point1, point2, pen);
                            _viewer.DrawLine(point2, point3, pen);
                            _viewer.DrawLine(point3, point1, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("中心点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;
                    case ObjectSnapMode.Tangent:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("切点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;
                    case ObjectSnapMode.Quad:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                            _viewer.DrawText("四分点", new Vector2(_currObjectSnapPoint.position.X + 15, _currObjectSnapPoint.position.Y - 15), pen1, rectPen);
                        }
                        break;
                    case ObjectSnapMode.Grid:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                        }
                        break;

                    default:
                        {
                            _viewer.DrawRectangle(new Vector2((float)(_currObjectSnapPoint.position.X - _threshold / 2), (float)(_currObjectSnapPoint.position.Y + _threshold / 2)),
                                _threshold, _threshold, pen);
                            _viewer.DrawCross(_currObjectSnapPoint.position, 3.6, pen);
                        }
                        break;
                }
            }
        }

        Vector2 Rotate(Vector2 vector, float angle)
        {
            float radians = (float)(Math.PI / 180 * angle);
            float cos = (float)Math.Cos(radians);
            float sin = (float)Math.Sin(radians);
            return new Vector2(
                vector.X * cos - vector.Y * sin,
                vector.X * sin + vector.Y * cos
            );
        }
    }
}
