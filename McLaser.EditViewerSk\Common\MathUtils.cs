﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;



namespace McLaser.EditViewerSk.Common
{
    public static class MathUtils
    {
        /// <summary>
        /// 点是否在矩形内
        /// </summary>
        //public static bool IsPointInRectangle(Vector2 point, LitMath.Rectangle2 rect)
        //{
        //    Vector2 rectLeftBottom = rect.leftBottom;
        //    Vector2 rectRightTop = rect.rightTop;

        //    if (point.X >= rectLeftBottom.x
        //        && point.X <= rectRightTop.x
        //        && point.Y >= rectLeftBottom.y
        //        && point.Y <= rectRightTop.y)
        //    {
        //        return true;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        /// <summary>
        /// Cross window
        /// https://yal.cc/rectangle-circle-intersection-test/
        /// </summary>
        //public static bool BoundingCross(Bounding bounding, Circle circle)
        //{
        //    Vector2 nearestPntOnBound = new Vector2(
        //        Math.Max(bounding.left, Math.Min(circle.center.x, bounding.right)),
        //        Math.Max(bounding.bottom, Math.Min(circle.center.y, bounding.top)));

        //    if (Vector2.Distance(nearestPntOnBound, circle.center) <= circle.radius)
        //    {
        //        double bdLeft = bounding.left;
        //        double bdRight = bounding.right;
        //        double bdTop = bounding.top;
        //        double bdBottom = bounding.bottom;

        //        return Vector2.Distance(new Vector2(bdLeft, bdTop), circle.center) >= circle.radius
        //            || Vector2.Distance(new Vector2(bdLeft, bdBottom), circle.center) >= circle.radius
        //            || Vector2.Distance(new Vector2(bdRight, bdTop), circle.center) >= circle.radius
        //            || Vector2.Distance(new Vector2(bdRight, bdBottom), circle.center) >= circle.radius;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        /// <summary>
        /// Cross window
        /// https://yal.cc/rectangle-circle-intersection-test/
        /// </summary>
        //public static bool BoundingCross(Bounding bounding, Ellipse ellipse)
        //{
        //    Vector2 nearestPntOnBound = new Vector2(
        //        Math.Max(bounding.left, Math.Min(ellipse.center.x, bounding.right)),
        //        Math.Max(bounding.bottom, Math.Min(ellipse.center.y, bounding.top)));

        //    if (Vector2.Distance(nearestPntOnBound, ellipse.center) <= ellipse.radiusX
        //        || Vector2.Distance(nearestPntOnBound, ellipse.center) <= ellipse.radiusY)
        //    {
        //        double bdLeft = bounding.left;
        //        double bdRight = bounding.right;
        //        double bdTop = bounding.top;
        //        double bdBottom = bounding.bottom;

        //        return Vector2.Distance(new Vector2(bdLeft, bdTop), ellipse.center) >= ellipse.radiusX
        //            || Vector2.Distance(new Vector2(bdLeft, bdBottom), ellipse.center) >= ellipse.radiusY
        //            || Vector2.Distance(new Vector2(bdRight, bdTop), ellipse.center) >= ellipse.radiusY
        //            || Vector2.Distance(new Vector2(bdRight, bdBottom), ellipse.center) >= ellipse.radiusY;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        /// <summary>
        /// 值是否在范围内
        /// </summary>
        public static bool IsValueInRange(double value, double min, double max)
        {
            return value >= min && value <= max;
        }

        /// <summary>
        /// 规整化弧度
        /// 返回值范围:[0, 2*PI)
        /// </summary>
        //public static double NormalizeRadianAngle(double rad)
        //{
        //    double value = rad % (2 * LitMath.Utils.PI);
        //    if (value < 0)
        //        value += 2 * LitMath.Utils.PI;
        //    return value;
        //}

        ///// <summary>
        ///// 镜像矩阵
        ///// </summary>
        //public static LitMath.Matrix3 MirrorMatrix(LitMath.Line2 mirrorLine)
        //{
        //    Vector2 lineDir = mirrorLine.direction;
        //    LitMath.Matrix3 matPos1 = LitMath.Matrix3.Translate(-mirrorLine.startPoint);
        //    double rotAngle = Vector2.SignedAngle(lineDir, new Vector2(1, 0));
        //    LitMath.Matrix3 matRot1 = LitMath.Matrix3.Rotate(rotAngle);

        //    LitMath.Matrix3 mirrorMatX = new LitMath.Matrix3(
        //        1, 0, 0,
        //        0, -1, 0,
        //        0, 0, 1);

        //    LitMath.Matrix3 matRot2 = LitMath.Matrix3.Rotate(-rotAngle);
        //    LitMath.Matrix3 matPos2 = LitMath.Matrix3.Translate(mirrorLine.startPoint);

        //    return matPos2 * matRot2 * mirrorMatX * matRot1 * matPos1;
        //}
    }







    internal static class MathHelper
    {
        public const float DegToRad = 0.01745329f;
        public const float RadToDeg = 57.29578f;
        private static float epsilon = 1E-06f;

        internal static float Epsilon
        {
            get => MathHelper.epsilon;
            set => MathHelper.epsilon = (double)value > 0.0 ? value : throw new ArgumentOutOfRangeException(nameof(value), (object)value, "The epsilon value must be a positive number greater than zero.");
        }

        internal static int Sign(float number) => !MathHelper.IsZero(number) ? Math.Sign(number) : 0;

        internal static int Sign(float number, float threshold) => !MathHelper.IsZero(number, threshold) ? Math.Sign(number) : 0;

        internal static bool IsOne(float number) => MathHelper.IsOne(number, MathHelper.Epsilon);

        internal static bool IsOne(float number, float threshold) => MathHelper.IsZero(number - 1f, threshold);

        public static bool IsZero(float number) => MathHelper.IsZero(number, MathHelper.Epsilon);

        public static bool IsZero(double number) => MathHelper.IsZero(number, (double)MathHelper.Epsilon);

        public static bool IsZero(float number, float threshold) => (double)number >= -(double)threshold && (double)number <= (double)threshold;

        public static bool IsZero(double number, double threshold) => number >= -threshold && number <= threshold;

        public static bool IsEqual(float a, float b) => MathHelper.IsEqual(a, b, MathHelper.Epsilon);

        public static bool IsEqual(double a, double b) => MathHelper.IsEqual(a, b, (double)MathHelper.Epsilon);

        public static bool IsEqual(float a, float b, float threshold) => MathHelper.IsZero(a - b, threshold);

        public static bool IsEqual(double a, double b, double threshold) => MathHelper.IsZero(a - b, threshold);

        public static float NormalizeAngle(float angle)
        {
            float number = angle % 360f;
            if (MathHelper.IsZero(number))
                number = 0.0f;
            return (double)number < 0.0 ? 360f + number : number;
        }

        internal static float PointLength(double x1, double y1, double x2, double y2) => (float)Math.Sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));

        internal static bool IntersectPointInCircle(
          double px,
          double py,
          double cx,
          double cy,
          double radius)
        {
            return (double)MathHelper.PointLength(px, py, cx, cy) <= radius;
        }

        internal static bool IntersectPointInRect(BoundingBox br, double x, double y, double limit = 0.05) =>
            !br.IsEmpty && 
            (double)br.Left - limit <= x && 
            ((double)br.Right + limit >= x && 
            (double)br.Bottom - limit <= y) && 
            (double)br.Top + limit >= y;

        internal static bool IntersectPointInLine(
          double x1,
          double y1,
          double x2,
          double y2,
          double px,
          double py,
          double limit = 0.05)
        {
            double num1 = MathHelper.PointLength(px, py, x1, y1);
            double num2 = MathHelper.PointLength(px, py, x2, y2);
            double num3 = MathHelper.PointLength(x1, y1, x2, y2);
            return num1 + num2 >= num3 - limit && num1 + num2 <= num3 + limit;
        }

        internal static bool IntersectLineInCircle(
          double x1,
          double y1,
          double x2,
          double y2,
          double cx,
          double cy,
          double radius = 0.05)
        {
            if (MathHelper.IntersectPointInCircle(x1, y1, cx, cy, radius) | MathHelper.IntersectPointInCircle(x2, y2, cx, cy, radius))
                return true;
            double x = MathHelper.PointLength(x1, y1, x2, y2);
            double num1 = ((cx - x1) * (x2 - x1) + (cy - y1) * (y2 - y1)) / Math.Pow(x, 2.0);
            double px = x1 + num1 * (x2 - x1);
            double py = y1 + num1 * (y2 - y1);
            if (!MathHelper.IntersectPointInLine(x1, y1, x2, y2, px, py))
                return false;
            double num2 = px - cx;
            double num3 = py - cy;
            return Math.Sqrt(num2 * num2 + num3 * num3) <= radius;
        }

        internal static bool IntersectLineInLine(
          double x1,
          double y1,
          double x2,
          double y2,
          double x3,
          double y3,
          double x4,
          double y4)
        {
            return MathHelper.IntersectLineInLine(x1, y1, x2, y2, x3, y3, x4, y4, out double _, out double _);
        }

        internal static bool IntersectLineInLine(
          double x1,
          double y1,
          double x2,
          double y2,
          double x3,
          double y3,
          double x4,
          double y4,
          out double collisionX,
          out double collisionY)
        {
            collisionX = collisionY = 0.0;
            if ((y4 - y3) * (x2 - x1) == (x4 - x3) * (y2 - y1) || (y4 - y3) * (x2 - x1) == (x4 - x3) * (y2 - y1))
                return false;
            double num1 = ((x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)) / ((y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1));
            double num2 = ((x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)) / ((y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1));
            if (num1 < 0.0 || num1 > 1.0 || (num2 < 0.0 || num2 > 1.0))
                return false;
            collisionX = x1 + num1 * (x2 - x1);
            collisionY = y1 + num1 * (y2 - y1);
            return true;
        }

        internal static bool IntersectLineInRect(
          BoundingBox br,
          double x1,
          double y1,
          double x2,
          double y2)
        {
            return !br.IsEmpty && ((br.Left > x1 || x1 > br.Right || (br.Bottom > y1 || y1 > br.Top)
                || (br.Left > x2 || x2 > br.Right || br.Bottom > y2) ? 0 : (y2 <= br.Top ? 1 : 0)) != 0
                || MathHelper.IntersectLineInLine(x1, y1, x2, y2, br.Left, br.Top, br.Left, br.Bottom, out double _, out double _) || (MathHelper.IntersectLineInLine(x1, y1, x2, y2, (double)br.Right, (double)br.Top, (double)br.Right, (double)br.Bottom, out double _, out double _) || MathHelper.IntersectLineInLine(x1, y1, x2, y2, (double)br.Left, (double)br.Top, (double)br.Right, (double)br.Top, out double _, out double _)) || MathHelper.IntersectLineInLine(x1, y1, x2, y2, (double)br.Left, (double)br.Bottom, (double)br.Right, (double)br.Bottom, out double _, out double _));
        }

        internal static bool IntersectLineInRectWithCollision(
          BoundingBox br,
          double startX,
          double startY,
          double endX,
          double endY,
          out List<Vector2> collisions)
        {
            collisions = new List<Vector2>();
            //List<Vector2> source = new List<Vector2>();
            //if (br.IsEmpty)
            //    return false;
            //if (((double)br.Left > startX || startX > (double)br.Right || ((double)br.Bottom > startY || startY > (double)br.Top) || ((double)br.Left > endX || endX > (double)br.Right || (double)br.Bottom > endY) ? 0 : (endY <= (double)br.Top ? 1 : 0)) != 0)
            //    return true;
            //double collisionX1;
            //double collisionY1;
            //if (MathHelper.IntersectLineInLine(startX, startY, endX, endY, br.Left, br.Top, br.Left, br.Bottom, out collisionX1, out collisionY1))
            //    source.Add(new Vector2((float)collisionX1, (float)collisionY1));
            //double collisionX2;
            //double collisionY2;
            //if (MathHelper.IntersectLineInLine(startX, startY, endX, endY, br.Right, br.Top, br.Right, br.Bottom, out collisionX2, out collisionY2))
            //    source.Add(new Vector2((float)collisionX2, (float)collisionY2));
            //double collisionX3;
            //double collisionY3;
            //if (MathHelper.IntersectLineInLine(startX, startY, endX, endY, br.Left, br.Top, br.Right, br.Top, out collisionX3, out collisionY3))
            //    source.Add(new Vector2((float)collisionX3, (float)collisionY3));
            //double collisionX4;
            //double collisionY4;
            //if (MathHelper.IntersectLineInLine(startX, startY, endX, endY, br.Left, br.Bottom, br.Right, br.Bottom, out collisionX4, out collisionY4))
            //    source.Add(new Vector2((float)collisionX4, (float)collisionY4));
            //collisions = source.Distinct<Vector2>().ToList<Vector2>();
            //if (2 == collisions.Count && !MathHelper.IsEqual(Vertex.Angle(new Vertex((float)startX, (float)startY), new Vertex((float)endX, (float)endY)), Vertex.Angle(new Vertex(collisions[0].X, collisions[0].Y), new Vertex(collisions[1].X, collisions[1].Y))))
            //{
            //    Vector2 vector2 = collisions[0];
            //    collisions[0] = collisions[1];
            //    collisions[1] = vector2;
            //}
            return collisions.Count > 0;
        }

        internal static bool IntersectRectInRect(BoundingBox a, BoundingBox b, double limit = 0.05) =>
              IntersectPointInRect(a, (double)b.Left, (double)b.Top, limit)
           || IntersectPointInRect(a, (double)b.Right, (double)b.Top, limit)
           || (IntersectPointInRect(a, (double)b.Left, (double)b.Bottom, limit)
           || IntersectPointInRect(a, (double)b.Right, (double)b.Bottom, limit))   || 
         
            (IntersectPointInRect(b, (double)a.Left, (double)a.Top, limit)
           || IntersectPointInRect(b, (double)a.Right, (double)a.Top, limit)
           || (IntersectPointInRect(b, (double)a.Left, (double)a.Bottom, limit)
           || IntersectPointInRect(b, (double)a.Right, (double)a.Bottom, limit)));

        internal static bool CollisionRectWithRect(BoundingBox a, BoundingBox b)
        {
            if (a.IsEmpty || b.IsEmpty)
                return false;
            return IntersectLineInLine(a.Left, a.Top, a.Left, a.Bottom, b.Left, b.Top, b.Left, b.Bottom)
                || IntersectLineInLine(a.Left, a.Top, a.Left, a.Bottom, b.Right, b.Top, b.Right, b.Bottom)
                || IntersectLineInLine(a.Left, a.Top, a.Left, a.Bottom, b.Left, b.Top, b.Right, b.Top)
                || IntersectLineInLine(a.Left, a.Top, a.Left, a.Bottom, b.Left, b.Bottom, b.Right, b.Bottom)
                || IntersectLineInLine(a.Right, a.Top, a.Right, a.Bottom, b.Left, b.Top, b.Left, b.Bottom)
                || IntersectLineInLine(a.Right, a.Top, a.Right, a.Bottom, b.Right, b.Top, b.Right, b.Bottom)
                || IntersectLineInLine(a.Right, a.Top, a.Right, a.Bottom, b.Left, b.Top, b.Right, b.Top)
                || IntersectLineInLine(a.Right, a.Top, a.Right, a.Bottom, b.Left, b.Bottom, b.Right, b.Bottom)
                || IntersectLineInLine(a.Left, a.Top, a.Right, a.Top, b.Left, b.Top, b.Left, b.Bottom)
                || IntersectLineInLine(a.Left, a.Top, a.Right, a.Top, b.Right, b.Top, b.Right, b.Bottom)
                || IntersectLineInLine(a.Left, a.Top, a.Right, a.Top, b.Left, b.Top, b.Right, b.Top)
                || IntersectLineInLine(a.Left, a.Top, a.Right, a.Top, b.Left, b.Bottom, b.Right, b.Bottom)
                || IntersectLineInLine(a.Left, a.Bottom, a.Right, a.Bottom, b.Left, b.Top, b.Left, b.Bottom)
                || IntersectLineInLine(a.Left, a.Bottom, a.Right, a.Bottom, b.Right, b.Top, b.Right, b.Bottom)
                || IntersectLineInLine(a.Left, a.Bottom, a.Right, a.Bottom, b.Left, b.Top, b.Right, b.Top)
                || IntersectLineInLine(a.Left, a.Bottom, a.Right, a.Bottom, b.Left, b.Bottom, b.Right, b.Bottom);
        }
    }
}
