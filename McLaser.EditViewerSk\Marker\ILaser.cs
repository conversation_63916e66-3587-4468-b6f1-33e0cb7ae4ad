﻿using System;
using System.ComponentModel;

namespace McLaser.EditViewerSk.Marker
{
    public interface ILaser : IDisposable, INotifyPropertyChanged
    {

        object SyncRoot { get; }


        int Index { get; set; }


        string Name { get; set; }


        //LaserType LaserType { get; }


        float MaxPowerWatt { get; set; }


        bool IsReady { get; }


        bool IsBusy { get; }


        bool IsError { get; }


   

        bool IsPowerControl { get; }

        bool IsShutterControl { get; }

        bool IsGuideControl { get; }


        bool IsTimedOut { get; }

        bool IsProtocolError { get; }


        object Tag { get; set; }

        bool CtlPower(float watt, string powerMapCategory = "");

        bool Initialize();


        bool CtlAbort();

        bool CtlReset();

        bool ListBegin();


        bool ListEnd();
    }


}
