﻿using McLaser.EditViewerSk.Convertors;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing.Design;

namespace McLaser.EditViewerSk.Marker
{
    //激光加工参数
    public class MarkArg
    {
        [DisplayName("a.加工速度")]    
        public double VccMark { get; set; } = 100;

        [DisplayName("b.加工加速度")]
        public double AccMark { get; set; } = 100;

        [DisplayName("c.跳转速度")]
        public double VccJump { get; set; } = 100;

        [DisplayName("d.跳转加速度")]
        public double AccJump { get; set; } = 100;

        [DisplayName("d.TA加速时间")]
        public double TA { get; set; } = 100;

        [DisplayName("d.TD减速时间")]
        public double TD { get; set; } = 100;

        [DisplayName("d.TS曲线时间")]
        public double TS { get; set; } = 100;

        [DisplayName("e.加工次数")]
        public int MarkCount { get; set; } = 1;

        [DisplayName("f.是否加工")]
        public bool EnableMark { get; set; } = true;

        [DisplayName("g.前置指令")]
        [Editor(typeof(MultilineStringEditor), typeof(UITypeEditor))]
        public string BeforeBuffer { get; set; } = "";

        [DisplayName("h.后置指令")]
        [Editor(typeof(MultilineStringEditor), typeof(UITypeEditor))]
        public string AfterBuffer { get; set; } = "";

    }
}
