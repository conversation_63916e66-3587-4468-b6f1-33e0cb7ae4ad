﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Dxf;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System.Drawing;
using System.Numerics;
using System.Windows.Input;

namespace McLaser.EditViewerSk.Commands
{
    /// <summary>
    /// 夹点移动命令
    /// </summary>
    public class GripPointMoveCmd : Command
    {
        /// <summary>
        /// 夹点
        /// </summary>
        protected GripPoint _gripPoint = null;
        protected int _index = -1;

        protected Vector2 _originalGripPos;
        protected Vector2 _resultGripPos;

        /// <summary>
        /// 图元
        /// </summary>
        protected EntityBase _entity = null;
        protected EntityBase _entityCopy = null;

        /// <summary>
        /// 鼠标位置(世界坐标系)
        /// </summary>
        protected Vector2 _mousePosInWorld;

        /// <summary>
        /// 构造函数
        /// </summary>
        public GripPointMoveCmd(EntityBase entity, int index, GripPoint gripPoint)
        {
            _entity = entity;
            _entityCopy = entity.Clone() as EntityBase;
            _index = index;
            _gripPoint = gripPoint;

            _originalGripPos = _gripPoint.position;
            _resultGripPos = _gripPoint.position;
            _mousePosInWorld = _gripPoint.position;
        }

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Initialize()
        {
            base.Initialize();

            //
            this.pointer.bIsShowAnchor = true;
            //this.pointer.mode = UI.Pointer.Mode.Locate;
        }

        /// <summary>
        /// 撤销
        /// </summary>
        public override void Undo()
        {
            base.Undo();
           //((EntityBase)_entity).SetGripPointAt(_index, _gripPoint, _originalGripPos);
        }

        /// <summary>
        /// 重做
        /// </summary>
        public override void Redo()
        {
            base.Redo();
            //_entity.SetGripPointAt(_index, _gripPoint, _resultGripPos);
        }

        /// <summary>
        /// 完成
        /// </summary>
        public override void Finish()
        {
            _resultGripPos = _mousePosInWorld;
            //_entity.SetGripPointAt(_index, _gripPoint, _resultGripPos);
            pointer.UpdateGripPoints();
            base.Finish();
        }

        /// <summary>
        /// 撤销
        /// </summary>
        public override void Cancel()
        {
            base.Cancel();
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            //if (e.Button == MouseButtons.Left)
            //{
            //    _mousePosInWorld = this.pointer.currentSnapPoint;
            //    _mgr.FinishCurrentCommand();
            //}

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            _mousePosInWorld = this.pointer.CurrentSnapPoint;
            //_entityCopy.SetGripPointAt(_index, _gripPoint, _mousePosInWorld);
            return EventResult.Handled;
        }

        public override EventResult OnKeyDown(KeyEventArgs e)
        {
            //if (e.KeyCode == Keys.Escape)
            //{
            //    _mgr.CancelCurrentCommand();
            //}

            return EventResult.Handled;
        }

        public override EventResult OnKeyUp(KeyEventArgs e)
        {
            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            ////this.anchor.OnDraw(_mgr.presenter, g, Color.Red);
            //this.DrawPath(_viewer);
            //_entityCopy.Render(_viewer);
        }

        protected virtual void DrawPath(ViewBase _viewer)
        {
            SKPaint paint = new SKPaint();
            _viewer.DrawLine( _originalGripPos, _mousePosInWorld);
        }
    }
}
