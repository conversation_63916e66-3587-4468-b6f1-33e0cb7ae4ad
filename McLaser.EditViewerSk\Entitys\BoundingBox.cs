using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Interfaces;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Entitys
{
    public enum Alignment
    {
        LeftTop,
        MiddleTop,
        RightTop,
        LeftMiddle,
        Center,
        RightMiddle,
        LeftBottom,
        MiddleBottom,
        RightBottom,
    }


    public class BoundingBox : IEquatable<BoundingBox>
    {
        protected double left;
        protected double right;
        protected double top;
        protected double bottom;
        protected double width;
        protected double height;
        private bool isNeedRegen;

        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public virtual Vector2 Min { get; internal set; }

        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public virtual Vector2 Max { get; internal set; }


        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public virtual Vector2 RealMin { get; internal set; }


        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public virtual Vector2 RealMax { get; internal set; }



        [JsonIgnore, Browsable(false)]
        public virtual EntityBase Parent { get; set; }


        public static BoundingBox Empty => new BoundingBox(0.0f, 0.0f, 0.0f, 0.0f)
        {
            IsEmpty = true
        };

        [Browsable(false)]
        public virtual bool IsEmpty { get; private set; }


        public virtual double Left
        {
            get => this.left;
            set
            {
                this.left = value;
                this.IsEmpty = false;
            }
        }


        public virtual double Right
        {
            get => this.right;
            set
            {
                this.right = value;
                this.IsEmpty = false;
            }
        }

        public virtual double Top
        {
            get => this.top;
            set
            {
                this.top = value;
                this.IsEmpty = false;
            }
        }


        public virtual double Bottom
        {
            get => this.bottom;
            set
            {
                this.bottom = value;
                this.IsEmpty = false;
            }
        }


        public virtual double Width
        {
            get
            {
                this.width = Math.Abs(this.Right - this.Left);
                return this.width;
            }
            set
            {
                if (0.0 >= value)
                    return;
                double num = value - this.width;
                this.left -= num / 2f;
                this.right += num / 2f;
                this.width = value;
                this.IsEmpty = false;
            }
        }


        public virtual double Height
        {
            get
            {
                this.height = Math.Abs(this.Top - this.Bottom);
                return this.height;
            }
            set
            {
                if (0.0 >= value)
                    return;
                double num = value - this.height;
                this.top += num / 2f;
                this.bottom -= num / 2f;
                this.height = value;
                this.IsEmpty = false;
            }
        }


        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public  Vector2 Center
        {
            get => new Vector2((this.Left + this.Right) / 2.0f, (this.Top + this.Bottom) / 2.0f);
            internal set
            {
                Vector2 vector2 = value - this.Center;
                this.Left += vector2.X;
                this.Right += vector2.X;
                this.Top += vector2.Y;
                this.Bottom += vector2.Y;
            }
        }


        [JsonIgnore, Browsable(false)]
        public virtual SKMatrix ModelMatrix
        {
            get
            {
                SKMatrix matrix = SKMatrix.Identity;
                if (this.Parent != null)
                {
                    matrix.PostConcat(this.Parent.ModelMatrix);
                }
                return matrix;
            }
        }




        public void Regen()
        {
            if (!this.IsEmpty)
            {
                this.BBoxRegen();
            }
            this.isNeedRegen = false;
        }

        private void BBoxRegen()
        {
            Vector2[] array = new Vector2[]
            {
                new  Vector2(this.Min.X, this.Min.Y),
                new  Vector2(this.Min.X, this.Max.Y),
                new  Vector2(this.Max.X, this.Max.Y),
                new  Vector2(this.Max.X, this.Min.Y),

            };
            Vector2[] array2 = new Vector2[array.Length];
            for (int i = 0; i < array.Length; i++)
            {
                var pt = ModelMatrix.MapPoint((float)array[i].X, (float)array[i].Y);
                Vector2 vector = new Vector2(pt.X, pt.Y);
                array2[i] = vector;
            }
            double x = array2.Min(v => v.X);
            double y = array2.Min(v => v.Y);
            double x2 = array2.Max(v => v.X);
            double y2 = array2.Max(v => v.Y);
            this.RealMin = new Vector2(x, y);
            this.RealMax = new Vector2(x2, y2);
        }


        [Browsable(false)]
        public virtual bool IsMarkerable { get; set; }

        [Browsable(false)]
        public virtual uint Repeat { get; set; }

        public override string ToString()
        {
            if (this.IsEmpty)
                return "(Empty)";
            return string.Format("{0:F3},{1:F3} ;{2:F3},{3:F3}", (object)this.Left, (object)this.Top, (object)this.Right, (object)this.Bottom);
        }

        public BoundingBox()
        {
            this.IsEmpty = true;
            this.IsMarkerable = true;
            this.Repeat = 1U;
        }

        public BoundingBox(double left, double top, double right, double bottom)
          : this()
        {
            this.Left = left;
            this.Top = top;
            this.Right = right;
            this.Bottom = bottom;
            this.IsEmpty = false;
        }


        public virtual BoundingBox Clone() => new BoundingBox()
        {

            Left = this.Left,
            Top = this.Top,
            Right = this.Right,
            Bottom = this.Bottom,
            IsEmpty = this.IsEmpty,
            Min = this.Min,
            Max = this.Max,
            RealMin = this.RealMin,
            RealMax = this.RealMax,
            Parent = this.Parent
        };

        public virtual bool Equals(BoundingBox other) => other != null && MathHelper.IsEqual(other.Left, this.Left) && (MathHelper.IsEqual(other.Top, this.Top) && MathHelper.IsEqual(other.Right, this.Right)) && MathHelper.IsEqual(other.Bottom, this.Bottom) && other.IsEmpty == this.IsEmpty;

        public virtual void Clear()
        {
            this.Left = this.Top = this.Right = this.Bottom = 0.0f;
            this.IsEmpty = true;
        }

        public virtual Vector2 LocationByAlign(Alignment align)
        {
            if (this.IsEmpty)
                return Vector2.Zero;
            switch (align)
            {
                case Alignment.LeftTop:
                    return new Vector2(this.Left, this.Top);
                case Alignment.MiddleTop:
                    return new Vector2(this.Center.X, this.Top);
                case Alignment.RightTop:
                    return new Vector2(this.Right, this.Top);
                case Alignment.LeftMiddle:
                    return new Vector2(this.Left, this.Center.Y);
                case Alignment.Center:
                    return this.Center;
                case Alignment.RightMiddle:
                    return new Vector2(this.Right, this.Center.Y);
                case Alignment.LeftBottom:
                    return new Vector2(this.Left, this.Bottom);
                case Alignment.MiddleBottom:
                    return new Vector2(this.Center.X, this.Bottom);
                case Alignment.RightBottom:
                    return new Vector2(this.Right, this.Bottom);
                default:
                    throw new InvalidOperationException("invalid alignment value !");
            }
        }

        public virtual void Union(BoundingBox br)
        {
            if (br == null || br.IsEmpty)
                return;
            if (this.IsEmpty)
            {
                this.Left = br.Left;
                this.Top = br.Top;
                this.Right = br.Right;
                this.Bottom = br.Bottom;
            }
            else
            {
                if ((double)this.Left > (double)br.Left)
                    this.Left = br.Left;
                if ((double)this.Top < (double)br.Top)
                    this.Top = br.Top;
                if ((double)this.Right < (double)br.Right)
                    this.Right = br.Right;
                if ((double)this.Bottom > (double)br.Bottom)
                    this.Bottom = br.Bottom;
            }
            this.IsEmpty = false;
        }

        public virtual void Union(double x, double y)
        {
            if (this.IsEmpty)
            {
                this.Left = x;
                this.Top = y;
                this.Right = x;
                this.Bottom = y;
            }
            else
            {
                if (this.Left > x)
                    this.Left = x;
                if (this.Top < y)
                    this.Top = y;
                if (this.Right < x)
                    this.Right = x;
                if (this.Bottom > y)
                    this.Bottom = y;
            }
            this.IsEmpty = false;
        }

        public virtual void Union(Vector2 v) => this.Union(v.X, v.Y);

        public virtual void Draw(ViewBase view)
        {
            if (this.IsEmpty)
                return;

            view.DrawRectangle(new Vector2(Left, Top), Width, Height);
        }


        public virtual void Transit(Vector2 delta)
        {
            if (this.IsEmpty)
                return;
            this.Left += delta.X;
            this.Right += delta.X;
            this.Top += delta.Y;
            this.Bottom += delta.Y;
        }

        public virtual bool HitTest(double x, double y, double threshold)
        {
            return Common.MathHelper.IntersectPointInRect(this, x, y, threshold);
        }

        public virtual bool HitTest(float left, float top, float right, float bottom, float threshold)
        {
            return this.HitTest(new BoundingBox(left, top, right, bottom), threshold);
        }

        public virtual bool HitTest(BoundingBox br, double threshold)
        {
            return (Common.MathHelper.IntersectRectInRect(this, br, threshold) || Common.MathHelper.CollisionRectWithRect(this, br));
        }

        public bool HitTest(IView view, Vector2 start, Vector2 end)
        {
            if (view == null)
            {
                return false;
            }
            this.Regen();
            if (this.IsEmpty)
            {
                return false;
            }
            double num =  Math.Min(start.X, end.X);
            double num2 = Math.Max(start.X, end.X) - num;
            double num3 = Math.Min(start.Y, end.Y);
            double num4 = Math.Max(start.Y, end.Y) - num3;
            return this.RealMin.X < num + num2 && num < this.RealMax.X && this.RealMin.Y < num3 + num4 && num3 < this.RealMax.Y;
        }

        /// <summary>
        /// 检查两个边界框是否相交（用于视口裁剪）
        /// </summary>
        /// <param name="other">另一个边界框</param>
        /// <returns>如果相交返回true</returns>
        public bool Intersects(BoundingBox other)
        {
            if (other == null || this.IsEmpty || other.IsEmpty)
                return false;

            return !(this.Right < other.Left ||
                     this.Left > other.Right ||
                     this.Bottom > other.Top ||
                     this.Top < other.Bottom);
        }

        /// <summary>
        /// 检查是否包含另一个边界框
        /// </summary>
        /// <param name="other">另一个边界框</param>
        /// <returns>如果包含返回true</returns>
        public bool Contains(BoundingBox other)
        {
            if (other == null || this.IsEmpty || other.IsEmpty)
                return false;

            return this.Left <= other.Left &&
                   this.Right >= other.Right &&
                   this.Top >= other.Top &&
                   this.Bottom <= other.Bottom;
        }
    }
}
