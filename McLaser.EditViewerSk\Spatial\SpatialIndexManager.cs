using System;
using System.Collections.Generic;
using System.Diagnostics;
using McLaser.EditViewerSk.Entitys;

namespace McLaser.EditViewerSk.Spatial
{
    /// <summary>
    /// 空间索引管理器，负责管理四叉树索引
    /// </summary>
    public class SpatialIndexManager
    {
        private QuadTreeNode _rootNode;
        private bool _isDirty = true;
        private BoundingBox _lastBounds = BoundingBox.Empty;

        /// <summary>
        /// 构建空间索引
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="bounds">索引边界</param>
        public void BuildIndex(IEnumerable<EntityBase> entities, BoundingBox bounds)
        {
            if (bounds.IsEmpty)
                return;

            var stopwatch = Stopwatch.StartNew();

            _rootNode?.Clear();
            _rootNode = new QuadTreeNode(bounds);

            int entityCount = 0;
            foreach (var entity in entities)
            {
                if (entity?.BoundingBox != null && !entity.BoundingBox.IsEmpty)
                {
                    _rootNode.Insert(entity);
                    entityCount++;
                }
            }

            _isDirty = false;
            _lastBounds = bounds;
            stopwatch.Stop();

#if DEBUG
            Debug.WriteLine($"空间索引构建完成: {entityCount} 个实体, 耗时 {stopwatch.ElapsedMilliseconds}ms");
            Debug.WriteLine($"索引统计: {_rootNode.GetStats()}");
#endif
        }

        /// <summary>
        /// 查询与指定范围相交的实体
        /// </summary>
        /// <param name="queryRange">查询范围</param>
        /// <returns>相交的实体列表</returns>
        public List<EntityBase> Query(BoundingBox queryRange)
        {
            if (_rootNode == null || queryRange.IsEmpty)
                return new List<EntityBase>();

            var stopwatch = Stopwatch.StartNew();
            var result = _rootNode.Query(queryRange);
            stopwatch.Stop();

#if DEBUG
            Debug.WriteLine($"空间查询完成: 找到 {result.Count} 个实体, 耗时 {stopwatch.ElapsedMilliseconds}ms");
#endif

            return result;
        }

        /// <summary>
        /// 检查索引是否需要重建
        /// </summary>
        /// <param name="currentBounds">当前边界</param>
        /// <returns>是否需要重建</returns>
        public bool NeedsRebuild(BoundingBox currentBounds)
        {
            return _isDirty || 
                   _rootNode == null || 
                   !_lastBounds.Equals(currentBounds) ||
                   HasSignificantBoundsChange(currentBounds);
        }

        /// <summary>
        /// 标记索引为脏状态，需要重建
        /// </summary>
        public void MarkDirty()
        {
            _isDirty = true;
        }

        /// <summary>
        /// 清空索引
        /// </summary>
        public void Clear()
        {
            _rootNode?.Clear();
            _rootNode = null;
            _isDirty = true;
            _lastBounds = BoundingBox.Empty;
        }

        /// <summary>
        /// 检查边界是否有显著变化
        /// </summary>
        /// <param name="newBounds">新边界</param>
        /// <returns>是否有显著变化</returns>
        private bool HasSignificantBoundsChange(BoundingBox newBounds)
        {
            if (_lastBounds.IsEmpty || newBounds.IsEmpty)
                return true;

            // 如果边界变化超过20%，则认为需要重建
            var widthChange = Math.Abs(newBounds.Width - _lastBounds.Width) / _lastBounds.Width;
            var heightChange = Math.Abs(newBounds.Height - _lastBounds.Height) / _lastBounds.Height;

            return widthChange > 0.2 || heightChange > 0.2;
        }

        /// <summary>
        /// 获取索引统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public string GetIndexStats()
        {
            if (_rootNode == null)
                return "索引未构建";

            return _rootNode.GetStats();
        }
    }
}
