﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Linq;

namespace McLaser.EditViewerSk.UndoRedo
{
    public enum SortMethod
    {
        ByCenter,
        ByBoundRect,
    }

    public enum EntitySort
    {
        TopToBottom,
        BottomToTop,
        LeftToRight,
        RightToLeft,
    }


    internal class UndoRedoEntitySort : UndoRedoSingle
    {
        private DocumentBase doc;
        private EntitySort sort;
        private SortMethod method;
        private EntityLayer layer;
        private List<EntityBase> list;
        private UndoRedoMultiple urs;

        public override void Execute()
        {
            this.urs = new UndoRedoMultiple();
            int index = this.list.Last<EntityBase>().Index;
            List<EntityBase> entityList;
            if (list.Count == 1 && list[0] is EntityGroup)
               entityList = new List<EntityBase>((this.list[0] as EntityGroup).Entities);
            else entityList = new List<EntityBase>((IEnumerable<EntityBase>)this.list);
            //switch (this.sort)
            //{
            //    case EntitySort.TopToBottom:
            //        if (this.method == SortMethod.ByCenter)
            //        {
            //            entityList.Sort((Comparison<EntityBase>)((a, b) => -a.BoundingBox.Center.Y.CompareTo(b.BoundingBox.Center.Y)));
            //            break;
            //        }
            //        entityList.Sort((Comparison<EntityBase>)((a, b) => -a.BoundingBox.Top.CompareTo(b.BoundingBox.Top)));
            //        break;
            //    case EntitySort.BottomToTop:
            //        if (this.method == SortMethod.ByCenter)
            //        {
            //            entityList.Sort((Comparison<EntityBase>)((a, b) => a.BoundingBox.Center.Y.CompareTo(b.BoundingBox.Center.Y)));
            //            break;
            //        }
            //        entityList.Sort((Comparison<EntityBase>)((a, b) => a.BoundingBox.Bottom.CompareTo(b.BoundingBox.Bottom)));
            //        break;
            //    case EntitySort.LeftToRight:
            //        if (this.method == SortMethod.ByCenter)
            //        {
            //            entityList.Sort((Comparison<EntityBase>)((a, b) => a.BoundingBox.Center.X.CompareTo(b.BoundingBox.Center.X)));
            //            break;
            //        }
            //        entityList.Sort((Comparison<EntityBase>)((a, b) => a.BoundingBox.Left.CompareTo(b.BoundingBox.Left)));
            //        break;
            //    case EntitySort.RightToLeft:
            //        if (this.method == SortMethod.ByCenter)
            //        {
            //            entityList.Sort((Comparison<EntityBase>)((a, b) => -a.BoundingBox.Center.X.CompareTo(b.BoundingBox.Center.X)));
            //            break;
            //        }
            //        entityList.Sort((Comparison<EntityBase>)((a, b) => -a.BoundingBox.Right.CompareTo(b.BoundingBox.Right)));
            //        break;
            //}

            var sortedSegments = new List<EntityBase>();
            var remainingSegments = new HashSet<dynamic>(entityList);
            try
            {
              

                // 任意选择一条线段作为起始点
                var currentSegment = remainingSegments.First();
                sortedSegments.Add(currentSegment);
                remainingSegments.Remove(currentSegment);
                while (remainingSegments.Count > 0)
                {
                    var found = false;

                    foreach (var segment in remainingSegments)
                    {

                        if (Math.Abs(currentSegment.EndPoint.X - segment.StartPoint.X) < 0.001 && Math.Abs(currentSegment.EndPoint.Y - segment.StartPoint.Y) < 0.001)
                        {
                            // 方向一致，加入结果列表
                            sortedSegments.Add(segment);
                            currentSegment = segment;
                            found = true;
                        }
                        else if (Math.Abs(currentSegment.EndPoint.X - segment.EndPoint.X) < 0.001 && Math.Abs(currentSegment.EndPoint.Y - segment.EndPoint.Y) < 0.001)
                        {
                            // 方向不一致，调整方向后加入结果列表
                            var tmp = ReverseDirection(segment);
                            sortedSegments.Add(tmp);
                            currentSegment = tmp;
                            found = true;
                        }

                        if (found)
                        {
                            remainingSegments.Remove(segment);
                            break;
                        }
                    }

                    // 如果没有找到匹配的线段，跳出循环（非连续路径）
                    if (!found)
                        return;
                }
            }
            catch (Exception ex)
            {

            }

            if (list.Count == 1 && list[0] is EntityGroup)
            {
                var group = ((EntityGroup)(list[0] as EntityGroup).Clone());
                group.Entities = sortedSegments;
                sortedSegments = new List<EntityBase>() { group };
            }

                this.urs = new UndoRedoMultiple();
            index = doc.ActiveLayer.Children.IndexOf(list.First());
            this.urs.Add((IUndoRedo)new UndoRedoEntityDelete(this.doc, this.list));
            this.urs.Add((IUndoRedo)new UndoRedoEntityAdd(this.doc, this.layer, sortedSegments, index));
            this.urs.Execute();

            //foreach (EntityBase entity in sortedSegments)
            //    this.urs.Add((IUndoRedo)new UndoRedoEntityMove(this.doc, entity, this.layer, index));
            //this.Redo();
        } 


        public dynamic ReverseDirection(dynamic segment)
        {
            if (segment is EntityLine)
            {
                var temp = segment.StartPoint;
                segment.StartPoint = segment.EndPoint;
                segment.EndPoint = temp;
                return segment;
            }
            else if (segment is EntityArc)
            {
                var temp = segment.StartAngle;
                segment.StartAngle = temp + segment.SweepAngle;
                //segment.EndAngle = temp - segment.SweepAngle;
                segment.SweepAngle = -segment.SweepAngle;
                return segment;
            }
            return segment;

        }
        public override void Undo() => this.urs.Undo();

        public override void Redo() => this.urs.Redo();

        public UndoRedoEntitySort(
          DocumentBase doc,
          EntityLayer layer,
          List<EntityBase> list,
          EntitySort sort,
          SortMethod method = SortMethod.ByCenter)
        {
            this.Name = "Entity Sort";
            this.doc = doc;
            this.layer = layer;
            this.list = new List<EntityBase>((IEnumerable<EntityBase>)list);
            this.sort = sort;
            this.method = method;
        }
    }
}
