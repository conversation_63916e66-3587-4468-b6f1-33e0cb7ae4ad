﻿using System;
using System.Numerics;

namespace McLaser.EditViewerSk.Marker
{
    public interface IMatrixStack : ICloneable
    {
        Matrix3 ToResult { get; }

        int Count { get; }

        void Clear();

        void Push(Matrix3 m);

        void Pop(out Matrix3 m);

        void Pop();

        void Push(double angle);

        void Push(double dx, double dy);

        void Push(Vector2 translate);

        void Push(double dx, double dy, double angle);
    }
}
