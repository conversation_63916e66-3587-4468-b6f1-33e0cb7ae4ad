﻿using System;
using System.ComponentModel;
using System.Drawing.Design;
using System.Windows.Forms.Design;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Convertors
{
    public class LongTextDropdownEditor : UITypeEditor
    {
        public override UITypeEditorEditStyle GetEditStyle(ITypeDescriptorContext context)
        {
            return UITypeEditorEditStyle.DropDown;
        }

        public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value)
        {
            if (provider != null)
            {
                var editorService = (IWindowsFormsEditorService)provider.GetService(typeof(IWindowsFormsEditorService));
                if (editorService != null)
                {
                    var textBox = new TextBox
                    {
                        Multiline = true,
                        ScrollBars = ScrollBars.Both,
                        Text = value as string,
                        WordWrap = true,
                        BorderStyle = BorderStyle.None,
                        AcceptsReturn = true,
                        Dock = DockStyle.Fill
                    };

                    var panel = new Panel
                    {
                        BorderStyle = BorderStyle.FixedSingle,
                        Size = new System.Drawing.Size(300, 100)
                    };
                    panel.Controls.Add(textBox);

                    textBox.LostFocus += (s, e) => editorService.CloseDropDown();

                    editorService.DropDownControl(panel);

                    return textBox.Text;
                }
            }

            return value;
        }
    }
}
