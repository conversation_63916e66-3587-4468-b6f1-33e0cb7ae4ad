﻿ 
using System;
using System.ComponentModel;

namespace McLaser.EditViewerSk.Entitys
{
    public class LwPolyLineVertex : EntityBase,IEquatable<LwPolyLineVertex>, ICloneable
    {
        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("X")]
        public double X { get; set; }

        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Y")]
        public double Y { get; set; }

        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Bulge")]
        public double Bulge { get; set; }

        [Browsable(true)]
        [ReadOnly(false)]
        [Category("Data")]
        [DisplayName("Ramp")]
        public double Ramp { get; set; }

        public LwPolyLineVertex(double x, double y, double bulge = 0.0f)
        {
            this.X = x;
            this.Y = y;
            this.Bulge = bulge;
            this.Ramp = 1f;
        }

        public LwPolyLineVertex(double x, double y, double bulge, double rampFactor = 1f)
          : this(x, y, bulge)
        {
            this.Ramp = rampFactor;
        }

        public LwPolyLineVertex(Vector2 v, float bulge = 0.0f)
        {
            this.X = v.X;
            this.Y = v.Y;
            this.Bulge = bulge;
            this.Ramp = 1f;
        }

        public LwPolyLineVertex()
        {
        }

        public bool Equals(LwPolyLineVertex other) => MathHelper.IsEqual(other.X, this.X) && MathHelper.IsEqual(other.Y, this.Y) && MathHelper.IsEqual(other.Bulge, this.Bulge) && MathHelper.IsEqual(other.Ramp, this.Ramp);

        public override string ToString() => string.Format("{0:F3}, {1:F3}: {2:F3}", (object)this.X, (object)this.Y, (object)this.Bulge);

        public new  object Clone() => (object)new LwPolyLineVertex()
        {
            X = this.X,
            Y = this.Y,
            Bulge = this.Bulge,
            Ramp = this.Ramp
        };

        public static LwPolyLineVertex Translate(LwPolyLineVertex vertex, Vector2 delta)
        {
            vertex.X += delta.X;
            vertex.Y += delta.Y;
            return vertex;
        }

        public static LwPolyLineVertex Rotate(
          LwPolyLineVertex vertex,
          double angle,
          Vector2 rotateCenter)
        {
            //Vector2 vector2 = Vector2.Transform(new Vector2(vertex.X, vertex.Y), Matrix3.CreateRotation(angle * ((float)Math.PI / 180f), rotateCenter));
            //vertex.X = vector2.X;
            //vertex.Y = vector2.Y;
            return vertex;
        }

        public static LwPolyLineVertex Scale(LwPolyLineVertex vertex, Vector2 scale)
        {
            Vector2 vector2 = new Vector2(vertex.X, vertex.Y) * scale;
            vertex.X = vector2.X;
            vertex.Y = vector2.Y;
            return vertex;
        }

        public static LwPolyLineVertex Scale(
          LwPolyLineVertex vertex,
          Vector2 scale,
          Vector2 center)
        {
            Vector2 vector2 = new Vector2(vertex.X - center.X, vertex.Y - center.Y) * scale;
            vertex.X = vector2.X + center.X;
            vertex.Y = vector2.Y + center.Y;
            return vertex;
        }

        public static LwPolyLineVertex operator -(
          LwPolyLineVertex left,
          LwPolyLineVertex right)
        {
            return new LwPolyLineVertex(left.X - right.X, left.Y - right.Y);
        }

        public static double Distance(LwPolyLineVertex v1, LwPolyLineVertex v2) => Math.Sqrt(((double)v1.X - (double)v2.X) * ((double)v1.X - (double)v2.X) + ((double)v1.Y - (double)v2.Y) * ((double)v1.Y - (double)v2.Y));

        public static double Angle(LwPolyLineVertex v1, LwPolyLineVertex v2) => LwPolyLineVertex.Angle(v2 - v1);

        public static double Angle(LwPolyLineVertex v)
        {
            double num = Math.Atan2((double)v.Y, (double)v.X);
            return num < 0.0 ? 2.0 * Math.PI + num : num;
        }
    }
}
