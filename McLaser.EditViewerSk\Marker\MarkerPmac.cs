﻿using McLaser.Core.Services.DeviceService.Base.Motion;
using McLaser.Devices.Motion;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;

using System;
using System.Threading;

namespace McLaser.EditViewerSk.Marker
{
    public class MeasurementBegin
    {
    }

    public class MeasurementEnd
    {
    }




    public class MarkerPmac : IMarker, IMarkCommand
    {
        private const float Tolerance = 0.001f;

        protected bool isThreadBusy;

        protected DocumentBase clonedDoc;

        protected DocumentBase originalDoc;

        protected Thread thread;

        protected bool isAborted;

        protected IMatrixStack oldMatrixStack;

        protected MeasurementBegin entityMeasurementBegin;

        protected MeasurementEnd entityMeasurementEnd;

        public virtual int Index { get; set; }

        public virtual string Name { get; set; }

        public virtual bool IsReady
        {
            get
            {
                if (MarkerArg == null)
                {
                    return false;
                }

                if (IsBusy)
                {
                    return false;
                }

                if (MarkerArg.MarkerCommand != null)
                {
                    return false;
                }



                return true;
            }
        }

        public virtual bool IsBusy
        {
            get
            {
                bool flag = false;


                if (MarkerArg != null && MarkerArg.Laser != null)
                {
                    flag |= MarkerArg.Laser.IsBusy;
                }

                return flag | isThreadBusy;
            }
        }

        public virtual bool IsError
        {
            get
            {
                bool flag = false;


                if (MarkerArg != null && MarkerArg.Laser != null)
                {
                    flag |= MarkerArg.Laser.IsError;
                }



                return flag;
            }
        }

        public virtual IMarkerArg MarkerArg { get; set; }

        public virtual IMotionCard Card { get; set; }

        public virtual double ScannerRotateAngle { get; set; }

        public virtual DocumentBase Document => clonedDoc;

        public virtual bool IsEnablePens { get; set; } = true;

        public uint MarkCounts { get; protected set; }

        public bool IsSimulationStop = false;   

        public virtual object Tag { get; set; }

        public event MarkerEventHandler OnStarted;

        public event MarkerEventHandler OnProgress;

        public event MarkerEventHandler OnFailed;

        public event MarkerEventHandler OnFinished;

        public event EventHandler OnBufferUpdate;

        public MarkerPmac(int index, IMotionCard card)
        {
            Index = index;
            Name = "MarkerDefault";
            MarkerArg = new MarkerArgDefault();
            ScannerRotateAngle = 0.0;
            isAborted = false;
            MarkCounts = 1;
            Card = card;
        }

        public MarkerPmac(int index, string name)
        {
            Name = name;
        }

        public virtual bool Ready(IMarkerArg markerArg)
        {
            MarkerArg = markerArg;
            if (markerArg == null)
            {
                return false;
            }

            if (markerArg.Document == null || markerArg.MarkerCommand == null || markerArg.Laser == null)
            {
                return false;
            }

            if (IsBusy)
            {
                return false;
            }

            //MarkerArg = markerArg.Clone();  
            clonedDoc = markerArg.Document;
            originalDoc = markerArg.Document;

            MarkerArg.Progress = 0.0;
            NotifyProgressing();
            return true;
        }

        public virtual bool Clear()
        {
            if (IsBusy)
            {
                return false;
            }

            //clonedDoc.New();
            return true;
        }

        public virtual bool Start(int index)
        {
            if (MarkerArg == null || MarkerArg.MarkerCommand == null || MarkerArg.Laser == null)
            {
                NotifyFailed();
                return false;
            }

            if (clonedDoc == null || clonedDoc.Layers.Count == 0)
            {
                NotifyFailed();
                return false;
            }
            MarkerArg.TasksIndex = index + 1;
            IsSimulationStop = false;
            isAborted = false;
            thread = new Thread(WorkerThread);
            thread.Name = "Marker: " + Name;
            thread.Priority = ThreadPriority.Normal;
            thread.Start();
            Thread.Sleep(100);
            //等待buffer完成
            bool active = true;
            bool running = true;
            while (true)
            {
                if (IsSimulationStop || MarkerArg.IsSimulation)
                {
                    return false;
                }
                var ret = (Card as CardPmac)?.IsCoordActive(MarkerArg.TasksIndex, ref active);
                ret &= (Card as CardPmac)?.IsCoordRunning(MarkerArg.TasksIndex, ref running);
                if (!active && !running)
                {
                    break;
                }
                Thread.Sleep(10);
            }


            return true;
        }

        public virtual bool ExternalStop()
        {
            if (MarkerArg == null)
            {
                return false;
            }

            bool result = false;
            if (MarkerArg.IsExternalStart)
            {

                NotifyFinished();
            }

            return result;
        }

        public virtual bool Stop()
        {
            if (MarkerArg == null)
            {
                return false;
            }

            if (MarkerArg.IsExternalStart)
            {
                return ExternalStop();
            }
            MarkerArg.MatrixStack.Clear();
            IsSimulationStop = true;    

            (Card as CardPmac)?.StopCoordinate(MarkerArg.TasksIndex);

            bool flag = true;
            isAborted = true;

            if (thread != null && thread.Join(50))
            {
                thread = null;
            }

            if (MarkerArg.IsExternalStart)
            {
                flag &= ExternalStop();
            }



            return flag;
        }

        public virtual bool Reset()
        {
            if (MarkerArg == null)
            {
                return false;
            }


            if (!IsBusy && !MarkerArg.IsExternalStart)
            {
                MarkerArg.Progress = 0.0;
                NotifyProgressing();
            }

            return true;
        }

        public virtual void NotifyStarted()
        {
            OnStarted?.Invoke(this, MarkerArg);
        }

        public virtual void NotifyProgressing()
        {
            OnProgress?.Invoke(this, MarkerArg);
        }

        public virtual void NotifyFailed()
        {
            OnFailed?.Invoke(this, MarkerArg);
        }

        public virtual void NotifyFinished()
        {
            OnFinished?.Invoke(this, MarkerArg);
            //OnBufferUpdate?.Invoke(MarkerArg.BufferCmd.ToString(), null);
        }

        protected virtual void WorkerThread()
        {
            NotifyStarted();
            isThreadBusy = true;
            MarkerArg.StartTime = DateTime.Now;
            bool flag = true;
            flag &= PreWork();
            flag &= MarkInitializtion(MarkerArg);
            flag &= MainWork();
            flag &= PostWork(flag);
            MarkerArg.IsSuccess = flag;

            isThreadBusy = false;
            if (flag)
            {
                if (!MarkerArg.IsExternalStart)
                {
                    //NotifyFinished();
                }
            }
            else
            {
                NotifyFailed();
            }
        }


        private bool IsPointsClose(Vector2 p1, Vector2 p2)
        {
            return Math.Abs(p1.X - p2.X) < Tolerance && Math.Abs(p1.Y - p2.Y) < Tolerance;
        }


        protected virtual bool PreWork()
        {
            try
            {
                oldMatrixStack = (IMatrixStack)MarkerArg.MatrixStack.Clone();
                entityMeasurementBegin = null;
                entityMeasurementEnd = null;
                MarkerArg.Progress = 0.0;
                MarkerArg.IsSuccess = false;
              
                Vector2 ptLastStart = new Vector2(-1000000, -100000);
                Vector2 ptLastEnd = new Vector2(-1000001, -100001);

                foreach (var layer in clonedDoc.Layers)
                {
                    foreach (dynamic item in layer.Children)
                    {
                        if (item is EntityGroup)
                        {
                            foreach (dynamic entity in (item as EntityGroup).Entities)
                            {
                                entity.IsNewStroke = !IsPointsClose(entity.StartPoint, ptLastEnd);

                                ptLastStart = entity.StartPoint;
                                ptLastEnd = entity.EndPoint;
                                entity.Arg.EnableMark = MarkerArg.EnableMark;
                            }
                        }
                        else
                        {
                            item.IsNewStroke = !IsPointsClose(item.StartPoint, ptLastEnd);

                            ptLastStart = item.StartPoint;
                            ptLastEnd = item.EndPoint;
                            item.Arg.EnableMark = MarkerArg.EnableMark;
                        }
                    }
                }
                if(!MarkerArg.IsSimulation)
                    MarkerArg.MatrixStack.Push(0, 0, 180);
                MarkerArg.MatrixStack.Push(clonedDoc.RotateOffset.X, clonedDoc.RotateOffset.Y, clonedDoc.RotateOffset.Angle);
                //MarkerArg.MatrixStack.Push(clonedDoc.RotateOffset.Angle);



                NotifyProgressing();
                MarkerArg.BufferCmd.Clear();
                if (MarkerArg.Laser.ListBegin())
                {
                    return true;
                }   
            }
            catch (Exception ex)
            {
            }

            return false;
        }

        protected virtual bool PostWork(bool success)
        {
            try
            {
                MarkerArg.MatrixStack.Clear();
             

                MarkerArg.MatrixStack = oldMatrixStack;
                MarkerArg.EndTime = DateTime.Now;
                TimeSpan timeSpan = MarkerArg.EndTime - MarkerArg.StartTime;
                MarkerArg.IsSuccess = success;
                ILaser laser = MarkerArg.Laser;
                success &= laser.ListEnd();
                if (!success)
                {
                }

                if (success)
                {
                    MarkCounts++;
                    NotifyFinished();
                }
                else
                {
                    NotifyFailed();
                }

            }
            catch (Exception ex)
            {
            }
            return true;
        }


        public virtual bool MarkInitializtion(IMarkerArg arg)
        {
            MarkerArg.BufferCmd = new System.Text.StringBuilder();

            var author = "lusunping";
            var version = "V1.0";
            arg.BufferCmd.AppendLine($"//Author: MCT {author}");
            arg.BufferCmd.AppendLine($"//Time: {DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}");
            arg.BufferCmd.AppendLine($"//Version: {version}");
            arg.BufferCmd.AppendLine();

            arg.BufferCmd.AppendLine($"close");
            arg.BufferCmd.AppendLine($"&{arg.TasksIndex}");
            arg.BufferCmd.AppendLine($"#{arg.MarkerCoord2D.AxisXIndex}->{arg.MarkerCoord2D.AxisXName}");
            arg.BufferCmd.AppendLine($"#{arg.MarkerCoord2D.AxisYIndex}->{arg.MarkerCoord2D.AxisYName}");

            arg.BufferCmd.AppendLine($"open prog Task{arg.TasksIndex}");
            arg.BufferCmd.AppendLine();
            return true;
        }

        protected virtual bool MainWork()
        {
            try
            {
                bool flag = true;

                int count = clonedDoc.Layers.Count;
                //加工
                foreach (var layer in Document.Layers)
                {
                    foreach (var entity in layer.Children)
                    {
                        entity.Mark(MarkerArg, this);
                    }
                }

                MarkerArg.BufferCmd.AppendLine($"close");

                Document.Action.PublishBuffer(MarkerArg.BufferCmd.ToString());

                if (MarkerArg.IsSimulation) return true;

                string recv = "";
                (Card as CardPmac)?.StopCoordinate(MarkerArg.TasksIndex);
                (Card as CardPmac)?.SendCommand(MarkerArg.BufferCmd.ToString(), ref recv);
                (Card as CardPmac)?.StartCoordinate(MarkerArg.TasksIndex, $"Task{MarkerArg.TasksIndex}");
                Thread.Sleep(500);
                //等待buffer完成
                bool active = true;
                bool running = true;
                while (true)
                {

                    var ret = (Card as CardPmac)?.IsCoordActive(MarkerArg.TasksIndex, ref active);
                    ret &= (Card as CardPmac)?.IsCoordRunning(MarkerArg.TasksIndex, ref running);
                    if (!active && !running)
                    {
                        break;
                    }
                    Thread.Sleep(10);
                }

                return flag;
            }
            catch (Exception ex)
            {
            }
            return false;
        }

        public virtual bool IsTargetLayer(EntityLayer layer)
        {
            return true;
        }

        public virtual bool IsTargetEntity(EntityBase entity)
        {
            return true;
        }


        public virtual bool MarkLinear(IMarkerArg arg, EntityLine line, out string cmd)
        {
            cmd = "";
            arg.IsNeedJump = false;
            string moveCmd = arg.IsNeedJump ? "Rapid" : "Linear";
            var ptStart = Vector2.Transform(line.StartPoint, arg.MatrixStack.ToResult);
            var ptEnd = Vector2.Transform(line.EndPoint, arg.MatrixStack.ToResult);
            string strAA = line.Arg.EnableMark ? "AA0.1" : "";
            if (arg.IsSimulation)
            {
                var length = Math.Sqrt(Math.Pow(ptEnd.X - ptStart.X, 2) + Math.Pow(ptEnd.Y - ptStart.Y, 2));
                double progress = 0;
                int time = 0;
                double speed = arg.SimulationSpeed;//100mm/s = 0.1mm/ms
                while (true)
                {
                    if(IsSimulationStop) return false;
                    Thread.Sleep(10);
                    double x = ptStart.X + progress * (ptEnd.X - ptStart.X);
                    double y = ptStart.Y + progress * (ptEnd.Y - ptStart.Y);
                    Vector2 vector = new Vector2(x, y);
                    arg.LaserDot = vector;
                    OnProgress?.Invoke(this, arg);
                    time += 10;
                    if (time > length / (speed / 1000))
                    {
                        break;
                    }
                    progress = time / (length / (speed / 1000));
                }
            }

            if (line.IsNewStroke)
                cmd = $"\r\n{"Rapid"} ABS F{line.Arg.VccJump}  {arg.MarkerCoord2D.AxisXName}{ptStart.X:F4} {arg.MarkerCoord2D.AxisYName}{ptStart.Y:F4}\r\n";

            if (line.Arg.BeforeBuffer != "")
            {
                cmd += $"\r\n{line.Arg.BeforeBuffer}\r\n";
            }
            cmd += $"{moveCmd} ABS F{line.Arg.VccMark} TA{line.Arg.TA} TD{line.Arg.TD} TS{line.Arg.TS} {arg.MarkerCoord2D.AxisXName}{ptEnd.X:F4}  {arg.MarkerCoord2D.AxisYName}{ptEnd.Y:F4} {strAA}";

            if (line.Arg.AfterBuffer != "")
            {
                cmd += $"\r\n{line.Arg.AfterBuffer}\r\n";
            }


            arg.BufferCmd.AppendLine(cmd);

            return true;
        }

        public virtual bool MarkArc(IMarkerArg arg, EntityArc arc, out string cmd)
        {
            cmd = "";
            string moveCmd = arc.IsCloseWise ? "CIRCLE2" : "CIRCLE1";
            var ptStart = Vector2.Transform(arc.StartPoint, arg.MatrixStack.ToResult);
            var ptEnd = Vector2.Transform(arc.EndPoint, arg.MatrixStack.ToResult);
            var ptCenter = Vector2.Transform(arc.Center, arg.MatrixStack.ToResult);
            var startAngle = arc.StartAngle + arg.Document.RotateOffset.Angle;
            string strAA = arc.Arg.EnableMark ? "AA0.1" : "";
            if (arc.BoundingBox == null) return true;
            if (arg.IsSimulation)
            {
                double radiusX = arc.BoundingBox.Width / 2;
                double radiusY = arc.BoundingBox.Height / 2;

                double avgRadius = (radiusX + radiusY) / 2;
                var length = Math.Abs(arc.SweepAngle) * Math.PI / 180.0f * arc.Radius;
                double progress = 0;
                int time = 0;
                double speed = arg.SimulationSpeed / 5;//100mm/s = 0.1mm/ms
                while (true)
                {
                    if (IsSimulationStop) return false;
                    Thread.Sleep(10);
                    double angle = startAngle + progress * arc.SweepAngle;
                    double radians = Math.PI * angle / 180.0f;
                    double x = ptCenter.X + (arc.Radius) * Math.Cos(radians);
                    double y = ptCenter.Y + (arc.Radius) * Math.Sin(radians);
                    Vector2 vector = new Vector2(x, y);
                    arg.LaserDot = vector;
                    OnProgress?.Invoke(this, arg);
                    time += 10;
                    if (time > length / (speed / 1000))
                    {
                        break;
                    }
                    progress = time / (length / (speed / 1000));
                }
            }



            if (arc.IsNewStroke)
                cmd = $"\r\n{"Rapid"} ABS F{arc.Arg.VccJump} TA{100} TD{100} TS{100} {arg.MarkerCoord2D.AxisXName}{ptStart.X:F4} {arg.MarkerCoord2D.AxisYName}{ptStart.Y:F4}\r\n";

            if (arc.Arg.BeforeBuffer != "")
            {
                cmd += $"\r\n{arc.Arg.BeforeBuffer}\r\n";
            }

            cmd += $"{moveCmd} ABS F{arc.Arg.VccMark} TA{arc.Arg.TA} TD{arc.Arg.TD} TS{arc.Arg.TS} {arg.MarkerCoord2D.AxisXName}{ptEnd.X:F4} {arg.MarkerCoord2D.AxisYName}{ptEnd.Y:F4} I{ptCenter.X - ptStart.X:F4} J{ptCenter.Y - ptStart.Y:F4} {strAA}";
            if (arc.Arg.AfterBuffer != "")
            {
                cmd += $"\r\n{arc.Arg.AfterBuffer}\r\n";
            }

            arg.BufferCmd.AppendLine(cmd);

            return true;
        }

        public virtual bool MarkCircle()
        {
            throw new NotImplementedException();
        }

        public virtual bool MarkBegin()
        {
            throw new NotImplementedException();
        }

        public virtual bool MarkEnd()
        {
            throw new NotImplementedException();
        }

        //自定义命令
        public virtual bool MarkCmd(IMarkerArg arg, EntityCmd entityCmd, out string cmd)
        {
            cmd = "";
            cmd += $"{entityCmd.Buffer}\r\n";
            arg.BufferCmd.AppendLine(cmd);
            return true;
        }


    }



    public enum PmacTask
    {
        Task00,
        Task01,
        Task02,
        Task03,
        Task04,
        Task05,
        Task06,
        Task07,
    }
}
