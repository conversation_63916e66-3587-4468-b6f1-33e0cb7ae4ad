﻿using McLaser.Core.Services.DeviceService.Base.Motion;
using McLaser.Devices.Motion;
using McLaser.EditViewerSk.Marker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace McLaser.EditViewerSk.Views
{
    /// <summary>
    /// BufferWindow.xaml 的交互逻辑
    /// </summary>
    public partial class BufferView : UserControl
    {
        private string _cmd = string.Empty;
        private IMotionCard _card;
        private IMarker _marker;
        public string BufferCmd
        {
            get { return _cmd; }
            set
            {
                _cmd = value;
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    rtb.Document.Blocks.Clear();
                    rtb.AppendText(_cmd);
                });

            }
        }
        public BufferView(IMarker marker)
        {
            InitializeComponent();
            _marker = marker;
            _card = marker.Card as CardPmac;
            _marker.OnBufferUpdate += _marker_OnBufferUpdate; 
        }

        private void _marker_OnBufferUpdate(object sender, EventArgs e)
        {
            BufferCmd = sender as string;
        }


        private void btnRunTest_Click(object sender, RoutedEventArgs e)
        {
            string recv = "";
            _card?.Stop(0);
            (_card as CardPmac)?.SendCommand(BufferCmd, ref recv);
            (_card as CardPmac)?.SendCommand($"&1b1r", ref recv);
        }
    }
}
