﻿using McLaser.Core;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;


namespace McLaser.EditViewerSk.ViewModels
{
    public class SkEditorViewModel : INotifyPropertyChanged
    {

        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        private DocumentBase doc;
        public DocumentBase Doc
        {
            get { return doc; }
            set { doc = value; OnPropertyChanged("Doc"); }
        }


        public SkEditorViewModel(DocumentBase doc)
        {
            this.Doc = doc;
            (this.Doc.View as ViewBase).MouseMove -= SkCanvas_MouseMove;
            (this.Doc.View as ViewBase).MouseMove += SkCanvas_MouseMove;
        }

        public Point _currentPosition = new Point();
        public Point CurrentPosition
        {
            get { return _currentPosition; }
            set { _currentPosition = value; OnPropertyChanged("CurrentPosition"); }
        }

        public ICommand BtnRedoCommand => new RelayCommand(() =>
        {
            Doc.Action.ActRedo();
        });

        public ICommand BtnUndoCommand => new RelayCommand(() =>
        {
            Doc.Action.ActUndo();
        });

        public ICommand BtnDrawPointCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawPoint();
        });

        public ICommand BtnDrawLineCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawLine();
        });

        public ICommand BtnDrawRectangleCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawRectangle();
        });

        public ICommand BtnDrawCircleCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawCircle();
        });

        public ICommand BtnDrawPolylineCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawPolyline();
        });

        public ICommand BtnDrawArcCommand => new RelayCommand(() =>
        {
            Doc.Action.ActDrawArc();
        });

        private void SkCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            var cur = e.GetPosition(sender as IInputElement);
            var pt = Doc.View.CanvasToModel(new Vector2(cur.X, cur.Y));
            CurrentPosition = new Point(pt.X, pt.Y);
        }

    }
}
