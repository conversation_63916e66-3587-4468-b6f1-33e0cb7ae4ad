// Updated by XamlIntelliSenseFileGenerator 2024/12/15 14:32:46
#pragma checksum "..\..\..\Views\SkCanvas.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "FF116A9890E69B67FB5C1330F58603C75726F2D820EAA9FE7DD5CA13B42EA271"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using McLaser.EditViewerSk;
using McLaser.EditViewerSk.ViewModels;
using McLaser.EditViewerSk.Views;
using PropertyTools.Wpf;
using SkiaSharp.Views.WPF;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Xceed.Wpf.Toolkit;
using Xceed.Wpf.Toolkit.Chart;
using Xceed.Wpf.Toolkit.Chromes;
using Xceed.Wpf.Toolkit.Converters;
using Xceed.Wpf.Toolkit.Core;
using Xceed.Wpf.Toolkit.Core.Converters;
using Xceed.Wpf.Toolkit.Core.Input;
using Xceed.Wpf.Toolkit.Core.Media;
using Xceed.Wpf.Toolkit.Core.Utilities;
using Xceed.Wpf.Toolkit.Mag.Converters;
using Xceed.Wpf.Toolkit.Panels;
using Xceed.Wpf.Toolkit.Primitives;
using Xceed.Wpf.Toolkit.PropertyGrid;
using Xceed.Wpf.Toolkit.PropertyGrid.Attributes;
using Xceed.Wpf.Toolkit.PropertyGrid.Commands;
using Xceed.Wpf.Toolkit.PropertyGrid.Converters;
using Xceed.Wpf.Toolkit.PropertyGrid.Editors;
using Xceed.Wpf.Toolkit.Zoombox;


namespace McLaser.EditViewerSk.Views
{


    /// <summary>
    /// SkCanvas
    /// </summary>
    public partial class SkCanvas : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector
    {


#line 196 "..\..\..\Views\SkCanvas.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SkiaSharp.Views.WPF.SKElement skContainer;

#line default
#line hidden

        private bool _contentLoaded;

        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent()
        {
            if (_contentLoaded)
            {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/McLaser.EditViewerSk;component/views/skcanvas.xaml", System.UriKind.Relative);

#line 1 "..\..\..\Views\SkCanvas.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);

#line default
#line hidden
        }

        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target)
        {
            switch (connectionId)
            {
                case 1:

#line 48 "..\..\..\Views\SkCanvas.xaml"
                    ((PropertyTools.Wpf.TreeListBox)(target)).SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TreeListBox_SelectionChanged_1);

#line default
#line hidden
                    return;
                case 2:

#line 54 "..\..\..\Views\SkCanvas.xaml"
                    ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ButtonLoadDxf_Click);

#line default
#line hidden
                    return;
                case 3:

#line 55 "..\..\..\Views\SkCanvas.xaml"
                    ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnTest_Click);

#line default
#line hidden
                    return;
                case 4:

#line 57 "..\..\..\Views\SkCanvas.xaml"
                    ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnUndo_Click);

#line default
#line hidden
                    return;
                case 5:

#line 58 "..\..\..\Views\SkCanvas.xaml"
                    ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnRedo_Click);

#line default
#line hidden
                    return;
                case 6:

#line 178 "..\..\..\Views\SkCanvas.xaml"
                    ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnOptimizer_Click);

#line default
#line hidden
                    return;
                case 7:

#line 179 "..\..\..\Views\SkCanvas.xaml"
                    ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDrawLine_Click);

#line default
#line hidden
                    return;
                case 8:
                    this.skContainer = ((SkiaSharp.Views.WPF.SKElement)(target));
                    return;
            }
            this._contentLoaded = true;
        }

        internal System.Windows.Controls.ContentControl MarkingControlView;
    }
}

