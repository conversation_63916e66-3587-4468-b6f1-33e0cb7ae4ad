using System;
using McLaser.Core.Common;
using McLaser.Core.Plugins;

namespace McLaser.App.Models
{
    /// <summary>
    /// 插件显示信息
    /// 用于在UI中显示插件的详细信息
    /// </summary>
    public partial class PluginDisplayInfo : ObservableObject
    {
        #region 私有字段

        private string _id = "";
        private string _name = "";
        private string _version = "";
        private string _description = "";
        private string _author = "";
        private string _category = "";
        private string _filePath = "";
        private bool _isLoaded = false;
        private PluginStatus _status = PluginStatus.NotLoaded;
        private DateTime _loadTime = DateTime.MinValue;
        private string _errorMessage = "";

        #endregion

        #region 属性

        /// <summary>
        /// 插件ID
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 插件名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => Set(ref _name, value);
        }

        /// <summary>
        /// 插件版本
        /// </summary>
        public string Version
        {
            get => _version;
            set => Set(ref _version, value);
        }

        /// <summary>
        /// 插件描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => Set(ref _description, value);
        }

        /// <summary>
        /// 插件作者
        /// </summary>
        public string Author
        {
            get => _author;
            set => Set(ref _author, value);
        }

        /// <summary>
        /// 插件类别
        /// </summary>
        public string Category
        {
            get => _category;
            set => Set(ref _category, value);
        }

        /// <summary>
        /// 插件文件路径
        /// </summary>
        public string FilePath
        {
            get => _filePath;
            set => Set(ref _filePath, value);
        }

        /// <summary>
        /// 是否已加载
        /// </summary>
        public bool IsLoaded
        {
            get => _isLoaded;
            set => Set(ref _isLoaded, value);
        }

        /// <summary>
        /// 插件状态
        /// </summary>
        public PluginStatus Status
        {
            get => _status;
            set => Set(ref _status, value);
        }

        /// <summary>
        /// 加载时间
        /// </summary>
        public DateTime LoadTime
        {
            get => _loadTime;
            set => Set(ref _loadTime, value);
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => Set(ref _errorMessage, value);
        }

        /// <summary>
        /// 状态显示文本
        /// </summary>
        public string StatusText => Status switch
        {
            PluginStatus.NotLoaded => "未加载",
            PluginStatus.Loading => "加载中",
            PluginStatus.Loaded => "已加载",
            PluginStatus.Initializing => "初始化中",
            PluginStatus.Initialized => "已初始化",
            PluginStatus.Starting => "启动中",
            PluginStatus.Running => "运行中",
            PluginStatus.Stopping => "停止中",
            PluginStatus.Stopped => "已停止",
            PluginStatus.Error => "错误",
            PluginStatus.Unloading => "卸载中",
            _ => "未知"
        };

        /// <summary>
        /// 状态颜色
        /// </summary>
        public string StatusColor => Status switch
        {
            PluginStatus.NotLoaded => "Gray",
            PluginStatus.Loading => "Orange",
            PluginStatus.Loaded => "Blue",
            PluginStatus.Initializing => "Orange",
            PluginStatus.Initialized => "Blue",
            PluginStatus.Starting => "Orange",
            PluginStatus.Running => "Green",
            PluginStatus.Stopping => "Orange",
            PluginStatus.Stopped => "Blue",
            PluginStatus.Error => "Red",
            PluginStatus.Unloading => "Orange",
            _ => "Gray"
        };

        /// <summary>
        /// 是否可以加载
        /// </summary>
        public bool CanLoad => !IsLoaded && Status == PluginStatus.NotLoaded;

        /// <summary>
        /// 是否可以卸载
        /// </summary>
        public bool CanUnload => IsLoaded && Status != PluginStatus.Running;

        /// <summary>
        /// 是否可以启动
        /// </summary>
        public bool CanStart => IsLoaded && (Status == PluginStatus.Loaded || Status == PluginStatus.Initialized || Status == PluginStatus.Stopped);

        /// <summary>
        /// 是否可以停止
        /// </summary>
        public bool CanStop => Status == PluginStatus.Running;

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName => System.IO.Path.GetFileName(FilePath);

        /// <summary>
        /// 文件大小（如果文件存在）
        /// </summary>
        public string FileSize
        {
            get
            {
                try
                {
                    if (System.IO.File.Exists(FilePath))
                    {
                        var fileInfo = new System.IO.FileInfo(FilePath);
                        var size = fileInfo.Length;
                        if (size < 1024)
                            return $"{size} B";
                        else if (size < 1024 * 1024)
                            return $"{size / 1024.0:F1} KB";
                        else
                            return $"{size / (1024.0 * 1024.0):F1} MB";
                    }
                }
                catch
                {
                    // 忽略错误
                }
                return "未知";
            }
        }

        /// <summary>
        /// 运行时间（如果正在运行）
        /// </summary>
        public string RunTime
        {
            get
            {
                if (Status == PluginStatus.Running && LoadTime != DateTime.MinValue)
                {
                    var elapsed = DateTime.Now - LoadTime;
                    if (elapsed.TotalDays >= 1)
                        return $"{elapsed.Days}天 {elapsed.Hours}小时";
                    else if (elapsed.TotalHours >= 1)
                        return $"{elapsed.Hours}小时 {elapsed.Minutes}分钟";
                    else if (elapsed.TotalMinutes >= 1)
                        return $"{elapsed.Minutes}分钟 {elapsed.Seconds}秒";
                    else
                        return $"{elapsed.Seconds}秒";
                }
                return "-";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化插件显示信息
        /// </summary>
        public PluginDisplayInfo()
        {
        }

        /// <summary>
        /// 从插件元数据创建显示信息
        /// </summary>
        /// <param name="metadata">插件元数据</param>
        /// <param name="filePath">文件路径</param>
        public PluginDisplayInfo(PluginMetadata metadata, string filePath)
        {
            Id = metadata.Id;
            Name = metadata.Name;
            Version = metadata.Version?.ToString() ?? "1.0.0";
            Description = metadata.Description;
            Author = metadata.Author;
            Category = metadata.Category ?? "General";
            FilePath = filePath;
        }

        #endregion

        #region 方法

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="newStatus">新状态</param>
        public void UpdateStatus(PluginStatus newStatus)
        {
            Status = newStatus;
            
            // 更新加载时间
            if (newStatus == PluginStatus.Running && LoadTime == DateTime.MinValue)
            {
                LoadTime = DateTime.Now;
            }
            else if (newStatus == PluginStatus.NotLoaded)
            {
                LoadTime = DateTime.MinValue;
                ErrorMessage = "";
            }

            // 通知相关属性变更
            OnPropertyChanged(nameof(StatusText));
            OnPropertyChanged(nameof(StatusColor));
            OnPropertyChanged(nameof(CanLoad));
            OnPropertyChanged(nameof(CanUnload));
            OnPropertyChanged(nameof(CanStart));
            OnPropertyChanged(nameof(CanStop));
            OnPropertyChanged(nameof(RunTime));
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            ErrorMessage = errorMessage;
            Status = PluginStatus.Error;
            OnPropertyChanged(nameof(StatusText));
            OnPropertyChanged(nameof(StatusColor));
        }

        /// <summary>
        /// 清除错误信息
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = "";
            if (Status == PluginStatus.Error)
            {
                Status = IsLoaded ? PluginStatus.Loaded : PluginStatus.NotLoaded;
            }
            OnPropertyChanged(nameof(StatusText));
            OnPropertyChanged(nameof(StatusColor));
        }

        #endregion
    }

    /// <summary>
    /// 统计项
    /// </summary>
    public partial class StatisticItem : ObservableObject
    {
        private string _name = "";
        private string _value = "";

        /// <summary>
        /// 统计项名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => Set(ref _name, value);
        }

        /// <summary>
        /// 统计项值
        /// </summary>
        public string Value
        {
            get => _value;
            set => Set(ref _value, value);
        }
    }

    /// <summary>
    /// 日志消息
    /// </summary>
    public partial class LogMessage : ObservableObject
    {
        private DateTime _timestamp = DateTime.Now;
        private LogLevel _level = LogLevel.Info;
        private string _message = "";
        private string _source = "";

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp
        {
            get => _timestamp;
            set => Set(ref _timestamp, value);
        }

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level
        {
            get => _level;
            set => Set(ref _level, value);
        }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Message
        {
            get => _message;
            set => Set(ref _message, value);
        }

        /// <summary>
        /// 消息来源
        /// </summary>
        public string Source
        {
            get => _source;
            set => Set(ref _source, value);
        }
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warn,
        Error,
        Fatal
    }
}
