﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using SkiaSharp;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Input;


namespace McLaser.EditViewerSk.Common
{


    /// <summary>
    /// 命令管理器
    /// </summary>
    public class CommandsMgr
    {


        public delegate void CommandEvent(Command cmd);
        public event CommandEvent commandFinished;
        public event CommandEvent commandCanceled;
        private List<Command> _undoCmds = new List<Command>();
        private List<Command> _redoCmds = new List<Command>();

        private ViewBase _viewer = null;
        public ViewBase Viewer => _viewer;

        public bool CanUndo => _undoCmds.Count > 0;
        public bool CanRedo => _redoCmds.Count > 0;


        private Command _currentCmd = null;
        public Command CurrentCmd => _currentCmd;


        public CommandsMgr(ViewBase viewer)
        {
            _viewer = viewer;
        }

     
        public void DoCommand(Command cmd)
        {
            if (_currentCmd != null)
            {
                return;
            }

            _currentCmd = cmd;
            _currentCmd.cmdMgr = this;
            _currentCmd.Initialize();
        }

       
        public void FinishCurrentCommand()
        {
            if (_currentCmd != null)
            {
                _currentCmd.Finish();
                if (_currentCmd is UndoCmd)
                {
                    this.Undo();
                }
                else if (_currentCmd is RedoCmd)
                {
                    this.Redo();
                }
                else
                {
                    _undoCmds.Add(_currentCmd);
                    _redoCmds.Clear();
                }

                commandFinished.Invoke(_currentCmd);
                _currentCmd = null;
            }
        }

 
        public void CancelCurrentCommand()
        {
            if (_currentCmd != null)
            {
                _currentCmd.Cancel();

                commandCanceled.Invoke(_currentCmd);
                _currentCmd = null;
            }
        }

    
        private void Undo()
        {
            if (_undoCmds.Count == 0)
            {
                return;
            }

            Command cmd = _undoCmds[_undoCmds.Count - 1];
            _undoCmds.RemoveAt(_undoCmds.Count - 1);
            cmd.Undo();
            _redoCmds.Add(cmd);
        }
 


        private void Redo()
        {
            if (_redoCmds.Count == 0)
            {
                return;
            }

            Command cmd = _redoCmds[_redoCmds.Count - 1];
            _redoCmds.RemoveAt(_redoCmds.Count - 1);
            cmd.Redo();
            _undoCmds.Add(cmd);
        }

       
        public void OnPaint(ViewBase _viewer)
        {
            if (_currentCmd != null)
            {
                _currentCmd.OnPaint(_viewer);
            }
        }

   
        public void OnMouseDown(MouseEventArgs e)
        {
            if (_currentCmd != null)
            {
                _currentCmd.OnMouseDown(e);
            }
        }

       
        public void OnMouseUp(MouseEventArgs e)
        {
            if (_currentCmd != null)
            {
                _currentCmd.OnMouseUp(e);
            }
        }

      
        public void OnMouseMove(MouseEventArgs e)
        {
            if (_currentCmd != null)
            {
                _currentCmd.OnMouseMove(e);
            }
        }

     
        public void OnKeyDown(KeyEventArgs e)
        {
            if (_currentCmd != null)
            {
                Command.EventResult eRet = _currentCmd.OnKeyDown(e);
                if (eRet.status == Command.EventResultStatus.Unhandled)
                {
                    if (e.Key == Key.Escape)
                    {
                        this.CancelCurrentCommand();
                    }
                }
            }
        }
       
        public void OnKeyUp(KeyEventArgs e)
        {
            if (_currentCmd != null)
            {
                Command.EventResult eRet = _currentCmd.OnKeyUp(e);
            }
        }
    }
}
