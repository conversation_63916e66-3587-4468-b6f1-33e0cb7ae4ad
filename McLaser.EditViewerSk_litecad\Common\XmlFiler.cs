﻿using System.Xml;
using System;
using System.Numerics;

namespace McLaser.EditViewerSk.Common
{
    ///// <summary>
    ///// SharpCAD XML 文件读写接口类
    ///// </summary>
    //public abstract class XmlFiler
    //{
    //    /// <summary>
    //    /// 写文件
    //    /// </summary>
    //    /// <param name="name">键名称</param>
    //    /// <param name="value">键值</param>
    //    /// <returns>
    //    /// 成功则返回true
    //    /// 失败则返回false
    //    /// </returns>
    //    public abstract bool Write(string name, string value);
    //    public abstract bool Write(string name, bool value);
    //    public abstract bool Write(string name, byte value);
    //    public abstract bool Write(string name, uint value);
    //    public abstract bool Write(string name, int value);
    //    public abstract bool Write(string name, double value);
    //    public abstract bool Write(string name, Vector2 value);
    //    public abstract bool Write(string name,McLaser.EditViewerSk.ColorST.Color color);
    //    public abstract bool Write(string name, ObjectId value);
    //    public abstract bool Write(string name,  LineWeight value);
    //    public abstract bool Write(string name,  LineType value);

    //    /// <summary>
    //    /// 读文件
    //    /// </summary>
    //    /// <param name="name">键名称</param>
    //    /// <param name="value">读取的键值</param>
    //    /// <returns>
    //    /// 成功则返回true
    //    /// 失败则返回false
    //    /// </returns>
    //    public abstract bool Read(string name, out string value);
    //    public abstract bool Read(string name, out bool value);
    //    public abstract bool Read(string name, out byte value);
    //    public abstract bool Read(string name, out uint value);
    //    public abstract bool Read(string name, out int value);
    //    public abstract bool Read(string name, out double value);
    //    public abstract bool Read(string name, out Vector2 value);
    //    public abstract bool Read(string name, out McLaser.EditViewerSk.ColorST.Color color);
    //    public abstract bool Read(string name, out ObjectId value);
    //    public abstract bool Read(string name, out  LineWeight value);
    //    public abstract bool Read(string name, out  LineType value);
    //}



    ///// <summary>
    ///// SharpCAD XML 文件读写具体实现类
    ///// </summary>
    //public class XmlFilerImpl : XmlFiler
    //{
    //    /// <summary>
    //    /// XML文档
    //    /// </summary>
    //    protected XmlDocument _xmldoc = null;
    //    public XmlDocument xmldoc
    //    {
    //        get { return _xmldoc; }
    //    }

    //    /// <summary>
    //    /// 当前XML节点
    //    /// </summary>
    //    protected XmlNode _curXmlNode = null;
    //    public XmlNode curXmlNode
    //    {
    //        get { return _curXmlNode; }
    //        set { _curXmlNode = value; }
    //    }

    //    /// <summary>
    //    /// 构造函数
    //    /// </summary>
    //    public XmlFilerImpl()
    //    {
    //        _xmldoc = new XmlDocument();
    //    }

    //    public XmlFilerImpl(XmlNode parentNode)
    //    {
    //        _curXmlNode = parentNode.OwnerDocument;
    //        _curXmlNode = parentNode;
    //    }

    //    public void Load(string xmlFileFullPath)
    //    {
    //        _xmldoc.Load(xmlFileFullPath);
    //    }

    //    public void Save(string fileFullPath)
    //    {
    //        if (_xmldoc != null)
    //        {
    //            _xmldoc.Save(fileFullPath);
    //        }
    //    }

    //    public void NewSubNodeAndInsert(string name)
    //    {
    //        XmlElement elem = _xmldoc.CreateElement(name);
    //        if (_curXmlNode != null)
    //        {
    //            _curXmlNode.AppendChild(elem);
    //        }
    //        else
    //        {
    //            _xmldoc.AppendChild(elem);
    //        }
    //        _curXmlNode = elem;
    //    }

    //    public void Pop()
    //    {
    //        _curXmlNode = _curXmlNode.ParentNode;
    //    }

    //    public bool _Write(string name, object value)
    //    {
    //        XmlElement elem = _xmldoc.CreateElement(name);
    //        if (elem == null)
    //        {
    //            return false;
    //        }
    //        elem.InnerText = value.ToString();
    //        _curXmlNode.AppendChild(elem);

    //        return true;
    //    }

    //    public override bool Write(string name, string value)
    //    {
    //        XmlElement elem = _xmldoc.CreateElement(name);
    //        if (elem == null)
    //        {
    //            return false;
    //        }
    //        elem.InnerText = value;
    //        _curXmlNode.AppendChild(elem);

    //        return true;
    //    }

    //    public override bool Write(string name, bool value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name, byte value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name, uint value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name, int value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name, double value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name, Vector2 value)
    //    {
    //        XmlElement elem = _xmldoc.CreateElement(name);
    //        if (elem == null)
    //        {
    //            return false;
    //        }
    //        elem.InnerText = value.X.ToString() + ";" + value.Y.ToString();
    //        _curXmlNode.AppendChild(elem);

    //        return true;
    //    }

    //    public override bool Write(string name,McLaser.EditViewerSk.ColorST.Color value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name, ObjectId value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name,  LineWeight value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Write(string name,  LineType value)
    //    {
    //        return _Write(name, value);
    //    }

    //    public override bool Read(string name, out string value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = "";
    //            return false;
    //        }
    //        value = node.InnerText;

    //        return true;
    //    }

    //    public override bool Read(string name, out bool value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = true;
    //            return false;
    //        }

    //        return bool.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out byte value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = 0;
    //            return false;
    //        }

    //        return byte.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out uint value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = 0;
    //            return false;
    //        }

    //        return uint.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out int value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = 0;
    //            return false;
    //        }

    //        return int.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out double value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = 0.0;
    //            return false;
    //        }

    //        return double.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out Vector2 value)
    //    {
    //        value = new Vector2(0, 0);
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            return false;
    //        }

    //        string strValue = node.InnerText;
    //        string[] xy = strValue.Split(';');
    //        if (xy.Length != 2)
    //        {
    //            return false;
    //        }

    //        double x = 0;
    //        if (!double.TryParse(xy[0], out x))
    //        {
    //            return false;
    //        }
    //        double y = 0;
    //        if (!double.TryParse(xy[1], out y))
    //        {
    //            return false;
    //        }
    //       // value.Set(x, y);

    //        return true;
    //    }

    //    public override bool Read(string name, out McLaser.EditViewerSk.ColorST.Color value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = ColorST.Color.ByLayer ;
    //            return false;
    //        }

    //        return ColorMethod.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out ObjectId value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = ObjectId.Null;
    //            return false;
    //        }

    //        return ObjectId.TryParse(node.InnerText, out value);
    //    }

    //    public override bool Read(string name, out  LineWeight value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = LineWeight.ByLineWeightDefault;
    //            return false;
    //        }

    //        // 
    //        value = (LineWeight)Enum.Parse(
    //            typeof( LineWeight), node.InnerText, true);
    //        return true;
    //    }

    //    public override bool Read(string name, out  LineType value)
    //    {
    //        XmlNode node = _curXmlNode.SelectSingleNode(name);
    //        if (node == null)
    //        {
    //            value = LineType.ByLineTypeDefault;
    //            return false;
    //        }

    //        // 
    //        value = (LineType)Enum.Parse(
    //            typeof( LineType), node.InnerText, true);
    //        return true;
    //    }
    //}
}
