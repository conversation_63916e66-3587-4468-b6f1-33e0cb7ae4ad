﻿<UserControl x:Class="McLaser.EditViewerSk.Views.SkEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:McLaser.EditViewerSk.ViewModels"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:McLaser.EditViewerSk" 
             xmlns:views ="clr-namespace:McLaser.EditViewerSk.Views"
             xmlns:base ="clr-namespace:McLaser.EditViewerSk.Entitys"
             xmlns:skia="clr-namespace:SkiaSharp.Views.WPF;assembly=SkiaSharp.Views.WPF" 
             xmlns:pt="http://propertytools.org/wpf" 
             xmlns:xctk="http://schemas.xceed.com/wpf/xaml/toolkit"
             xmlns:cv="clr-namespace:McLaser.EditViewerSk.ViewModels" 
             xmlns:winfm="clr-namespace:System.Windows.Forms;assembly=System.Windows.Forms"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" 
             d:DataContext="{d:DesignInstance Type=vm:SkEditorViewModel}">

    <UserControl.Resources>
        <Style x:Key="NoButtonStyle" TargetType="{x:Type ToggleButton}">
            <Style.Setters>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ToggleButton">
                            <ContentPresenter HorizontalAlignment="Center"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style.Setters>
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Setter Property="FontWeight" Value="Bold"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>


    <Grid >
        <Grid.ColumnDefinitions>

            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="1"/>
        </Grid.ColumnDefinitions>


        <DockPanel  LastChildFill="True">
            <Label Content="{Binding Doc.FileName}" Height="28" DockPanel.Dock="Top" Background="AliceBlue"/>
            <Label Content="{Binding}" Height="30" DockPanel.Dock="Bottom" Visibility="Collapsed"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>

                <DockPanel  LastChildFill="True">
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Top">
                        <Button Content="上移" />
                        <Button Content="下移" />
                        <Button Content="移到最前" />
                        <Button Content="移到最后" />
                    </StackPanel>
                    <pt:TreeListBox Name="tv" HierarchySource="{Binding Doc.Layers,Mode=OneWay}" 
                pt:TreeListBoxDragDropHelper.IsDragSource="True" 
                pt:TreeListBoxDragDropHelper.IsDropTarget="True"
                MouseLeftButtonUp="tv_MouseLeftButtonUp" Background="AliceBlue" KeyUp="tv_KeyUp">
                        <pt:TreeListBox.ContextMenu>
                            <ContextMenu>
                                <pt:EnumMenuItem Header="插入测高使能" Tag="1" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入C轴跟随使能" Tag="2" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入Z轴跟随使能" Tag="3" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入激光初始化" Tag="4" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入清除当前补偿表" Tag="5" Click="InsertEntityCmd_Click" />
                                <pt:EnumMenuItem Header="插入延时" Tag="6" Click="InsertEntityCmd_Click" />
                            </ContextMenu>
                        </pt:TreeListBox.ContextMenu>

                        <pt:TreeListBox.ItemTemplate>
                            <HierarchicalDataTemplate DataType="{x:Type base:EntityBase}" ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <Image Source="{Binding Icon}" Width="18" Height="18"/>
                                    <TextBlock Text="{Binding Name}" Margin="5, 0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </pt:TreeListBox.ItemTemplate>


                        <!--<pt:TreeListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="ContextMenu">
                                    <Setter.Value>
                                        <ContextMenu>
                                            <pt:EnumMenuItem Header="插入测高使能" Tag="1" Click="InsertEntityCmd_Click" />
                                            <pt:EnumMenuItem Header="插入C轴跟随使能" Tag="2" Click="InsertEntityCmd_Click" />
                                            <pt:EnumMenuItem Header="插入Z轴跟随使能" Tag="3" Click="InsertEntityCmd_Click" />
                                            <pt:EnumMenuItem Header="插入激光初始化" Tag="4" Click="InsertEntityCmd_Click" />
                                            <pt:EnumMenuItem Header="插入清除当前补偿表" Tag="5" Click="InsertEntityCmd_Click" />
                                        </ContextMenu>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </pt:TreeListBox.ItemContainerStyle>-->

                    </pt:TreeListBox>

                </DockPanel>

                <DockPanel LastChildFill="True" Grid.Column="1">
                    <ToolBarTray DockPanel.Dock="Top">
                        <ToolBar>
                            <Button Content="{local:Image Icons/Toolbars/101.png}" Click="btnNew_Click" ToolTip="新建"/>
                            <Button Content="{local:Image Icons/Toolbars/102.png}" Click="btnOpen_Click" ToolTip="打开"/>
                            <Button Content="{local:Image Icons/Toolbars/103.png}" Click="btnSave_Click" ToolTip="保存"/>
                            <Button Content="{local:Image Icons/Toolbars/undo.png}" Click="btnUndo_Click" ToolTip="撤回"/>
                            <Button Content="{local:Image Icons/Toolbars/redo.png}" Click="btnRedo_Click" ToolTip="重做"/>
                            <Button Content="{local:Image Icons/Toolbars/copy.png}" Click="btnCopy_Click" ToolTip="复制"/>
                            <Button Content="{local:Image Icons/Toolbars/cut.png}" Click="btnCut_Click" ToolTip="剪切"/>
                            <Button Content="{local:Image Icons/Toolbars/paste.png}" Click="btnPaste_Click" ToolTip="粘贴"/>

                            <Button Content="{local:Image Icons/Toolbars/simul_delete.png}" Click="btnDelete_Click" ToolTip="删除"/>
                            <Button Content="{local:Image Icons/Toolbars/shape_group.png}" Click="btnGroup_Click" ToolTip="组合"/>
                            <Button Content="{local:Image Icons/Toolbars/shape_ungroup.png}" Click="btnUnGroup_Click" ToolTip="取消组合"/>
                            <Button Content="{local:Image Icons/Toolbars/reverssort.png}" Click="btnSort_Click" ToolTip="排序"/>
                            <Button Content="{local:Image Icons/Toolbars/Reverse.png}" Click="btnReverse_Click" ToolTip="方向反转"/>
                            <Button Content="{local:Image Icons/Toolbars/109.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/110.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/111.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/112.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/113.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/114.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/115.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/116.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/117.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/118.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/119.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/120.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/121.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/122.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/123.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/124.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/125.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/126.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/127.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/128.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/129.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/130.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/131.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/132.png}"/>
                        </ToolBar>
                    </ToolBarTray>
                    <ToolBarTray  DockPanel.Dock="Left" Orientation="Vertical">
                        <ToolBar Band="1">
                            <Button Content="{local:Image Icons/Toolbars/201.png}" Command="{Binding BtnDrawPointCommand}" ToolTip="绘制点"/>
                            <Button Content="{local:Image Icons/Toolbars/202.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/203.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/204.png}" Command="{Binding BtnDrawRectangleCommand}"  ToolTip="绘制矩形"/>
                            <Button Content="{local:Image Icons/Toolbars/205.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/206.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/207.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/208.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/209.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/210.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/211.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/212.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/213.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/214.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/215.png}"/>

                        </ToolBar>
                        <ToolBar Band="2">

                            <Button Content="{local:Image Icons/Toolbars/216.png}" />
                            <Button Content="{local:Image Icons/Toolbars/line.png}" Command="{Binding BtnDrawLineCommand}" ToolTip="绘制直线"/>
                            <Button Content="{local:Image Icons/Toolbars/217.png}" Command="{Binding BtnDrawPolylineCommand}" ToolTip="绘制多段直线"/>
                            <Button Content="{local:Image Icons/Toolbars/218.png}" Command="{Binding BtnDrawCircleCommand}" ToolTip="绘制圆"/>
                            <Button Content="{local:Image Icons/Toolbars/219.png}" Command="{Binding BtnDrawArcCommand}" ToolTip="绘制圆弧"/>
                            <Button Content="{local:Image Icons/Toolbars/220.png}" />
                            <Button Content="{local:Image Icons/Toolbars/img_dxf.png}" Click="btnLoadDxf_Click" ToolTip="导入dxf"/>
                            <Button Content="{local:Image Icons/Toolbars/222.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/223.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/224.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/225.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/226.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/227.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/228.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/229.png}"/>
                            <Button Content="{local:Image Icons/Toolbars/230.png}"/>

                        </ToolBar>
                    </ToolBarTray>
                    <StatusBar DockPanel.Dock="Bottom">
                        <StatusBarItem>
                            <ToggleButton Width="60">
                                <ToggleButton.Style>
                                    <Style TargetType="{x:Type ToggleButton}" BasedOn="{StaticResource NoButtonStyle}">
                                        <Setter Property="Content" Value="CPlane"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter Property="Content">
                                                    <Setter.Value>
                                                        <TextBlock Text="World"/>
                                                    </Setter.Value>
                                                </Setter>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </ToggleButton.Style>
                            </ToggleButton>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="60">
                                <TextBlock Text="x" Margin="0 0 4 0"/>
                                <TextBlock Text="{Binding CurrentPosition.X, StringFormat='{}{0:0.000}'}"/>
                            </StackPanel>
                        </StatusBarItem>
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="60">
                                <TextBlock Text="y" Margin="0 0 4 0"/>
                                <TextBlock Text="{Binding CurrentPosition.Y, StringFormat='{}{0:0.000}'}"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <StackPanel Orientation="Horizontal" Width="80">
                                <Rectangle Width="12" Height="12" Margin="4" Fill="Black"/>
                                <TextBlock Text="默认" VerticalAlignment="Center"/>
                            </StackPanel>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <ToggleButton Content="捕获" IsChecked="True" Style="{StaticResource NoButtonStyle}" Width="40"/>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <ToggleButton Content="显示轨迹" IsChecked="True" Style="{StaticResource NoButtonStyle}" Width="50"/>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <ToggleButton Content="实时刷新" Style="{StaticResource NoButtonStyle}" Width="50"/>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <ToggleButton Content="锚定" Style="{StaticResource NoButtonStyle}" Width="40"/>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                            <ToggleButton Content="记录数据" Style="{StaticResource NoButtonStyle}" Width="100"/>
                        </StatusBarItem>
                        <Separator/>
                        <StatusBarItem>
                        </StatusBarItem>
                    </StatusBar>
                    <StatusBar DockPanel.Dock="Right" Width="35">
                        <StatusBarItem VerticalAlignment="Top">
                            <DockPanel  Margin="0">
                                <Label Background="White" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Gray" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orange" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Yellow" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Blue" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Magenta" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Cyan" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orchid" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Red" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Green" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="White" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Gray" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orange" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Yellow" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Blue" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Magenta" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Cyan" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Orchid" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Red" DockPanel.Dock="Top" Width="25" Height="25"/>
                                <Label Background="Green" DockPanel.Dock="Top" Width="25" Height="25"/>
                            </DockPanel>
                        </StatusBarItem>
                    </StatusBar>
                    <skia:SKElement x:Name="skContainer" Focusable="True"/>
                </DockPanel>


                <WindowsFormsHost   VerticalAlignment="Top" Grid.Column="2">
                    <winfm:PropertyGrid x:Name="wfpg" Dock="Fill"></winfm:PropertyGrid >
                </WindowsFormsHost>

            </Grid>

        </DockPanel>





        <!--<TabControl Grid.Column="2" >

            <TabItem Header="加工" Visibility="Collapsed">
                <ContentControl x:Name="MarkingControlView"/>
            </TabItem>

            <TabItem Header="参数">
                <WindowsFormsHost   VerticalAlignment="Top">
                    <winfm:PropertyGrid x:Name="wfpg" Dock="Fill"></winfm:PropertyGrid >
                </WindowsFormsHost>
            </TabItem>

            <TabItem Header="脚本Buffer" Visibility="Collapsed">
                <ContentControl x:Name="BufferView"/>
            </TabItem>
        </TabControl>-->

        <GridSplitter Grid.Column="1" ResizeDirection="Columns" VerticalAlignment="Stretch" HorizontalAlignment="Left" Width="2" Margin="-4,0,0,0" Background="Transparent"/>

    </Grid>



</UserControl>