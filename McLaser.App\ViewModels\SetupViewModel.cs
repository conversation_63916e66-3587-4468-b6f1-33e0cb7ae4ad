﻿using McLaser.EditViewerSk.Marker;
using McLaser.EditViewerSk.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace McLaser.App.ViewModels
{
    public class SetupViewModel : ObservableObject
    {
        private SkEditor _skEditor;

        public FrameworkElement SkControl
        {
            get
            {
                if(_skEditor == null)
                {
                    _skEditor = new SkEditor(new MarkerPmac(0,""));
                   
                }
                return _skEditor;
            }
        }


        public SetupViewModel()
        {
           
        }

       
    }
}
