﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Marker;
using McLaser.EditViewerSk.UndoRedo;
using McLaser.EditViewerSk.ViewModels;
using PropertyTools.Wpf;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using KeyEventArgs = System.Windows.Input.KeyEventArgs;
using OpenFileDialog = Microsoft.Win32.OpenFileDialog;
using SaveFileDialog = Microsoft.Win32.SaveFileDialog;
using UserControl = System.Windows.Controls.UserControl;
using ViewBase = McLaser.EditViewerSk.Base.ViewBase;

namespace McLaser.EditViewerSk.Views
{

    public partial class SkEditor : UserControl
    {

        public SKTypeface Font;
        public DocumentBase doc;
        private ViewBase viewer;
        private IMarker _marker;

        public DocumentBase internalDoc;


        public DocumentBase Document
        {
            get { return doc; }
            set
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    if (value == null || value.Equals((object)this.doc))
                        return;
                    else
                    {
                        this.doc = value;
                    }

                    if (viewer == null)
                    {
                        //// 1. 初始化 OpenGL 上下文（如 GLFW、WGL、EGL 等）
                        //// 2. 创建 GRGlInterface
                        //var glInterface = GRGlInterface.Create();
                        //if (glInterface == null || !glInterface.Validate())
                        //    throw new Exception("GRGlInterface 创建失败");

                        //// 3. 创建 GRContext
                        //var grContext = GRContext.CreateGl(glInterface);

                        //// 4. 创建 GPU 渲染表面
                        //var framebufferInfo = new GRGlFramebufferInfo(0, SKColorType.Rgba8888.ToGlSizedFormat());
                        //var renderTarget = new GRBackendRenderTarget(800, 600, 0, 8, framebufferInfo);
                        //var surface = SKSurface.Create(grContext, renderTarget, GRSurfaceOrigin.BottomLeft, SKColorType.Rgba8888);

                        //// 5. 绘制
                        //var canvas = surface.Canvas;
                        //canvas.Clear(SKColors.Green);

                        viewer = new ViewBase(this.skContainer, doc);
                    }
                    else
                        viewer.Document = doc;

                    doc.View = viewer;
                    doc.Action.viewer = viewer;
                    DataContext = new SkEditorViewModel(doc);

                    //doc.Action.Init(doc, _viewer);
                    if (this.doc.Layers.Count == 0)
                        doc.Action.ActLayerNew();
                    doc.ActiveLayer = doc.Layers[0];
                    doc.SelectedEntityChanged -= Document_SelectedEntityChanged;
                    doc.SelectedEntityChanged += Document_SelectedEntityChanged;
                    viewer.RepaintCanvas();
                });

            }
        }

        public SkEditor(IMarker marker)
        {
            InitializeComponent();

            this.PreviewKeyDown += SkCanvas_PreviewKeyDown;
            this.PreviewKeyUp += SkCanvas_PreviewKeyUp;

            internalDoc = new DocumentBase();
            Document = internalDoc;
            DataContext = new SkEditorViewModel(doc);


            marker.OnProgress += _marker_OnProgress;

            doc.Action.EventMarkingViewRegisted += Action_EventMarkingViewRegisted;
            wfpg.PropertyValueChanged += Wfpg_PropertyValueChanged;
        }

        private void _marker_OnProgress(IMarker sender, IMarkerArg markerArg)
        {
            viewer.DrawDot(markerArg.LaserDot.X, markerArg.LaserDot.Y);
        }

        private void Wfpg_PropertyValueChanged(object s, PropertyValueChangedEventArgs e)
        {
            viewer?.RepaintCanvas();
        }

        private void Action_EventMarkingViewRegisted(object sender, EventArgs e)
        {
            try
            {
                //if (sender is System.Windows.Forms.Control)
                //    this.MarkingControlView.Content = new WindowsFormsHost() { Child = (sender as System.Windows.Forms.Form) };
                //else if (sender is System.Windows.Controls.Control)
                //    this.MarkingControlView.Content = sender as System.Windows.Forms.Control;
            }
            catch (Exception ex)
            {

            }
        }

        private void Document_SelectedEntityChanged(object sender, EventArgs e)
        {
            try
            {
                if (Document.SelectedEntitys == null || Document.SelectedEntitys.Count == 0)
                {
                    wfpg.SelectedObjects = new object[] { Document.ActiveLayer };
                }
                else
                    wfpg.SelectedObjects = Document.SelectedEntitys.ToArray();
                ExpandAllProperties(wfpg);
            }
            catch (Exception ex)
            {

            }
        }

        private void ExpandAllProperties(System.Windows.Forms.PropertyGrid grid)
        {

            grid.Refresh();
            if (grid.SelectedObject != null && grid.Controls.Count > 0)
            {
                var propertyGridView = grid.Controls[0] as System.Windows.Forms.Control;
                if (propertyGridView != null)
                {
                    foreach (GridItem item in wfpg.SelectedGridItem.Parent?.GridItems ?? grid.SelectedGridItem.GridItems)
                    {
                        ExpandGridItem(item);
                    }
                }
            }
        }

        private void ExpandGridItem(GridItem item)
        {
            if (item.Expandable)
            {
                item.Expanded = true;
                foreach (GridItem childItem in item.GridItems)
                {
                    ExpandGridItem(childItem);
                }
            }
        }

        private void SkCanvas_PreviewKeyUp(object sender, KeyEventArgs e)
        {
            viewer.SkContainer_KeyUp(sender, e);
        }

        private void SkCanvas_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            viewer.SkContainer_KeyDown(sender, e);
        }

        private void btnDrawLine_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActDrawLine();
        }

        private void btnLoadDxf_Click(object sender, RoutedEventArgs e)
        {

            doc.Action.ActReadDxf();
        }

        private void btnNew_Click(object sender, RoutedEventArgs e)
        {
            Document = new DocumentBase();

            doc.Action.ActLayerNew();
        }

        private void btnOpen_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择文件",
                Filter = "所有文件 (*.*)|*.*|mc文件(*.mc)|*.mc ",
                FilterIndex = 1,
                Multiselect = false // 设置是否允许选择多个文件
            };


            if (openFileDialog.ShowDialog() == true)
            {
                Document = Action.ActLoad(openFileDialog.FileName);
                Document.FileName = openFileDialog.FileName;
                Document.View.OnZoomFit();
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(doc.FileName))
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "mct marker data files (*.mc)|*.mc|All Files (*.*)|*.*";
                sfd.FileName = string.Empty;
                sfd.Title = "另存为 ...";
                if (sfd.ShowDialog() == false)
                    return;

                doc.FileName = sfd.FileName;
            }

            doc.Action.ActSave(doc, doc.FileName);

        }

        private void btnRedo_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActRedo();
        }

        private void btnUndo_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActUndo();
        }

        private void btnDelete_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityDelete(doc.SelectedEntitys);
        }

        private void btnGroup_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityGroup(doc.SelectedEntitys);
        }

        private void btnUnGroup_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityUnGroup(doc.SelectedEntitys);

        }

        private void btnSort_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntitySort(doc.SelectedEntitys, doc.ActiveLayer, EntitySort.LeftToRight);
        }

        private void btnReverse_Click(object sender, RoutedEventArgs e)
        {
            doc.Action.ActEntityReverse(doc.SelectedEntitys, doc.ActiveLayer);
        }

        private void tv_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            List<EntityBase> list = new List<EntityBase>();
            doc.SelectedEntitys.Clear();
            foreach (var item in tv.SelectedItems)
            {
                list.Add((EntityBase)item);
            }
            doc.SelectedEntitys = list;
            viewer.RepaintCanvas();
        }

        private void tv_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Up || e.Key == Key.Down)
            {
                if (e.IsUp)
                {
                    List<EntityBase> list = new List<EntityBase>();
                    doc.SelectedEntitys.Clear();
                    foreach (var item in tv.SelectedItems)
                    {
                        list.Add((EntityBase)item);
                    }
                    doc.SelectedEntitys = list;
                    viewer.RepaintCanvas();
                }
            }
        }

        private void btnCopy_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnCut_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnPaste_Click(object sender, RoutedEventArgs e)
        {

        }

        private void InsertEntityCmd_Click(object sender, RoutedEventArgs e)
        {
            //"插入测高使能"  
            //"插入C轴跟随使能"  
            //"插入Z轴跟随使能" 
            //"插入激光初始化" 
            //"插入清除当前补偿表"
            var btn = (sender as EnumMenuItem).Tag.ToString();
            int index = tv.SelectedIndex - 1;
            switch (btn)
            {
                case "1":
                    doc.ActiveLayer.Insert(index, new EntityActionRange());
                    break;
                case "2":
                    doc.ActiveLayer.Insert(index, new EntityActionFollow());
                    break;
                case "3":
                    doc.ActiveLayer.Insert(index, new EntityActionCaliRange());
                    break;
                case "4":
                    doc.ActiveLayer.Insert(index, new EntityActionInitLaser());
                    break;
                case "5":
                    doc.ActiveLayer.Insert(index, new EntityActionClearCali());
                    break;
                case "6":
                    doc.ActiveLayer.Insert(index, new EntityActionDwell());
                    break;
                default:
                    break;
            }
        }
    }
}
