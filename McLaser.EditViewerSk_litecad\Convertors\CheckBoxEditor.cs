﻿using System;
using System.ComponentModel;
using System.Drawing.Design;
using System.Windows.Forms;

namespace McLaser.EditViewerSk.Convertors
{
    public class CheckBoxEditor : UITypeEditor
    {
        public override UITypeEditorEditStyle GetEditStyle(ITypeDescriptorContext context)
        {
            return UITypeEditorEditStyle.None;  
        }

        public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value)
        {
            return value;  
        }

        public override void PaintValue(PaintValueEventArgs e)
        {
            
            bool isChecked = (bool)e.Value;
            ControlPaint.DrawCheckBox(e.Graphics, e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height,
                isChecked ? ButtonState.Checked : ButtonState.Normal);
        }
    }
}
