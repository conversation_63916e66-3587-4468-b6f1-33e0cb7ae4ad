﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Commands;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Windows.Input;
using System.Linq;

public class LineCmd : DrawCmd
{
    public EntityLine _line = null;
    public EntityLine CurLine { get { return _line; } }

    private DocumentBase doc;

    public LineCmd(DocumentBase doc)
    {
        this.doc = doc;
    }


    /// <summary>
    /// 新增的图元
    /// </summary>
    protected override IEnumerable<EntityBase> newEntities
    {
        get { return new EntityLine[1] { CurLine }; }
    }

    /// <summary>
    /// 步骤
    /// </summary>
    private enum Step
    {
        Step1_SpecifyStartPoint = 1,
        Step2_SpecifyEndPoint = 2,
    }
    private Step _step = Step.Step1_SpecifyStartPoint;

    /// <summary>
    /// 初始化
    /// </summary>
    public override void Initialize()
    {
        base.Initialize();

        _step = Step.Step1_SpecifyStartPoint;
        this.pointer.Mode = IndicatorMode.Locate;
    }

    protected override void Commit()
    {
        if (this.newEntities.Count() == 1)
        {
            doc.Action.ActEntityAdd(newEntities.First());
        }
    }

    public override EventResult OnMouseDown(MouseEventArgs e)
    {
        if (_step == Step.Step1_SpecifyStartPoint)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                _line = new EntityLine();
                _line.StartPoint = this.pointer.CurrentSnapPoint;
                _line.EndPoint = this.pointer.CurrentSnapPoint;

           

                _step = Step.Step2_SpecifyEndPoint;
            }
        }
        else if (_step == Step.Step2_SpecifyEndPoint)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                _line.EndPoint = this.pointer.CurrentSnapPoint;
                _mgr.FinishCurrentCommand();
            }
        }

        return EventResult.Handled;
    }

    public override EventResult OnMouseUp(MouseEventArgs e)
    {
        return EventResult.Handled;
    }

    public override EventResult OnMouseMove(MouseEventArgs e)
    {
        if (_step == Step.Step2_SpecifyEndPoint)
        {
            _line.EndPoint = this.pointer.CurrentSnapPoint;
        }

        return EventResult.Handled;
    }

    public override void OnPaint(ViewBase _viewer)
    {
        //if (_line != null)
        //{
        //    ViewBase viewer = _mgr.Viewer as ViewBase;
        //    _line.Render(viewer);
        //}
    }
}