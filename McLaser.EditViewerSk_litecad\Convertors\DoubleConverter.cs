﻿using System;
using System.ComponentModel;
using System.Globalization;

namespace McLaser.EditViewerSk.Convertors
{
    public class DoubleConverter : TypeConverter
    {
        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
        {
            return sourceType == typeof(string) || base.CanConvertFrom(context, sourceType);
        }

        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
        {
            if (value is string str)
            {
                if (double.TryParse(str, NumberStyles.Any, culture, out double result))
                {
                    return Math.Round(result, 6); // 输入时四舍五入到两位小数
                }
                else
                {
                    throw new ArgumentException("无效的数值格式");
                }
            }
            return base.ConvertFrom(context, culture, value);
        }

        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            if (destinationType == typeof(string) && value is double d)
            {
                return d.ToString("F2", culture); // 显示两位小数
            }
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}
