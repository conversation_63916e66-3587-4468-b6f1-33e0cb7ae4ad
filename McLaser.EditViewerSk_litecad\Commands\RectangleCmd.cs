﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Common;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;

namespace McLaser.EditViewerSk.Commands
{
    public class RectangleCmd : DrawCmd
    {
        private EntityRectangle _rectangle = null;
        private DocumentBase doc;

        public RectangleCmd(DocumentBase doc)
        {
            this.doc = doc;
        }



        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityRectangle[1] { _rectangle }; }
        }

        /// <summary>
        /// 步骤
        /// </summary>
        private Step _step = Step.Step1_SpecifyStartPoint;
        private enum Step
        {
            Step1_SpecifyStartPoint = 1,
            Step2_SpecifyEndPoint = 2,
        }

        public override void Initialize()
        {
            base.Initialize();

            _step = Step.Step1_SpecifyStartPoint;
            this.pointer.Mode = IndicatorMode.Locate;
        }

        protected override void Commit()
        {
            if (this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }

        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SpecifyStartPoint:
                    if (e.LeftButton == MouseButtonState.Pressed)
                    {
                        _rectangle = new EntityRectangle();
                        _rectangle.StartPoint = this.pointer.CurrentSnapPoint;
                        _rectangle.EndPoint = this.pointer.CurrentSnapPoint;
                        //_rectangle.layerId = this.Document.currentLayerId;
                        //_rectangle.color = this.Document.currentColor;
                        _step = Step.Step2_SpecifyEndPoint;
                    }
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecifyEndPoint)
            {
                _rectangle.EndPoint = this.pointer.CurrentSnapPoint;
                _mgr.FinishCurrentCommand();
            }
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (_step == Step.Step2_SpecifyEndPoint)
            {
                _rectangle.EndPoint = this.pointer.CurrentSnapPoint;
                //_viewer.RepaintCanvas();
            }

            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {

            //if (_rectangle != null)
            //{
            //    ViewBase viewer = _mgr.Viewer as ViewBase;
            //    _rectangle.Render(viewer);
            //}
        }
    }
}
