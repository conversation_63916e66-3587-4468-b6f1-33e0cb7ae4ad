﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace McLaser.EditViewerSk.Marker
{
    //传递标记参数对象
    public class MarkerArgDefault : ICloneable, IMarkerArg
    {
        [XmlIgnore]
        protected List<Offset> offsets;

        [XmlIgnore]
        public virtual DocumentBase Document { get; set; }
        [XmlIgnore]
        public virtual IMarkCommand MarkerCommand { get; set; }
        [XmlIgnore]
        public virtual ILaser Laser { get; set; }


        public virtual MarkTargets MarkTargets { get; set; }

        [XmlIgnore]
        public virtual List<ViewBase> ViewTargets { get; set; }

        public virtual bool IsGuided { get; set; }

        public virtual uint RepeatSelected { get; set; }

        public virtual float SpeedSelected { get; set; }

        public virtual bool IsExternalStart { get; set; }
        public virtual bool IsNeedJump { get; set; }
        [XmlIgnore]
        public virtual StringBuilder  BufferCmd { get; set; }

        public virtual bool IsJumpToOriginAfterFinished { get; set; }

        [XmlIgnore]
        public virtual IMatrixStack MatrixStack { get; set; }


        public virtual List<Offset> Offsets
        {
            get
            {
                return offsets;
            }
            set
            {
                offsets = value;
            }
        }

        public uint OffsetIndex { get; set; }

        public virtual string OffsetAndTextDataTargetEntityName { get; set; }
        public virtual bool IsSimulation { get; set; }
        public virtual double SimulationSpeed { get; set; }
        public Vector2 LaserDot { get; set; }
        public int TasksIndex { get; set; }

        public MarkerCoordinate2D MarkerCoord2D { get; set; }

        public virtual DateTime StartTime { get; set; }

        public virtual DateTime EndTime { get; set; }

        public virtual double Progress { get; set; }

        public virtual bool IsSuccess { get; set; }

        public virtual bool IsVerifyScannerPowerFault { get; set; }

        public virtual bool IsMeasurementToPolt { get; set; }

        public int MeasurementPlotProgram { get; set; }

        public virtual bool IsRegisteringFonts { get; set; }

        public virtual bool IsEnablePens { get; set; }
        [XmlIgnore]
        public virtual object Tag { get; set; }
        public virtual bool  EnableMark { get; set; }
        public MarkerArgDefault()
        {
            
            offsets = new List<Offset>();
            OffsetIndex = 0u;
            StartTime = DateTime.Now;
            MarkTargets = MarkTargets.All;
            ViewTargets = new List<ViewBase>();
            IsGuided = false;
            RepeatSelected = 1u;
            SpeedSelected = 0f;
            IsExternalStart = false;
            IsNeedJump = true;
            IsEnablePens = true;
            IsVerifyScannerPowerFault = false;
            IsRegisteringFonts = false;
            BufferCmd = new StringBuilder();
            MatrixStack = new MatrixStack();
            MarkerCoord2D = new MarkerCoordinate2D(1,"X",2,"Y");
        }

     
        public MarkerArgDefault(DocumentBase doc, IMarkCommand card)
            : this()
        {
            Document = doc;
            MarkerCommand = card;
        }

 
        public virtual object Clone()
        {
            MarkerArgDefault markerArgDefault = new MarkerArgDefault
            {
                MarkerCommand = MarkerCommand,

                MarkTargets = MarkTargets,
                IsGuided = IsGuided,
                RepeatSelected = RepeatSelected,
                SpeedSelected = SpeedSelected,
                IsExternalStart = IsExternalStart,
                IsJumpToOriginAfterFinished = IsJumpToOriginAfterFinished,
                OffsetIndex = OffsetIndex,
                OffsetAndTextDataTargetEntityName = OffsetAndTextDataTargetEntityName,
                StartTime = StartTime,
                EndTime = EndTime,
                Progress = Progress,
                IsSuccess = IsSuccess,
                IsEnablePens = IsEnablePens,
                IsVerifyScannerPowerFault = IsVerifyScannerPowerFault,
                IsRegisteringFonts = IsRegisteringFonts,
                IsMeasurementToPolt = IsMeasurementToPolt,
                MeasurementPlotProgram = MeasurementPlotProgram,
                Tag = Tag,
                MarkerCoord2D = MarkerCoord2D,
                MatrixStack = MatrixStack
            };

            foreach (Offset offset in offsets)
            {
                markerArgDefault.Offsets.Add(offset.Clone());
            }

            if (Document != null)
            {
                markerArgDefault.Document = (DocumentBase)Document.Clone();
            }

            markerArgDefault.ViewTargets = new List<ViewBase>(ViewTargets);
            return markerArgDefault;
        }
 
 
    }
}
