﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;
using System.Collections.Generic;
using System.IO;

namespace McLaser.EditViewerSk.UndoRedo
{
    internal struct UndoRedoEntityDeleteLayer
    {
        public int Index;
        public EntityLayer Layer;
    }

    internal struct UndoRedoEntityDeleteEntity
    {
        public int IndexLayer;
        public EntityLayer Layer;
        public int IndexEntity;
        public EntityBase Entity;
    }

    internal class UndoRedoEntityDelete : UndoRedoSingle
    {
        private DocumentBase doc;
        private EntityLayer layer;
        private EntityBase entity;
        private List<EntityBase> list;

        private List<UndoRedoEntityDeleteLayer> targetLayer;
        private List<UndoRedoEntityDeleteEntity> targetEntity;

        public override void Execute() => this.Redo();

        public override void Undo()
        {
            foreach (UndoRedoEntityDeleteLayer entityDeleteLayer in this.targetLayer)
                this.doc.Layers.Insert(entityDeleteLayer.Index, entityDeleteLayer.Layer);
            foreach (UndoRedoEntityDeleteEntity entityDeleteEntity in this.targetEntity)
                this.doc.Layers[entityDeleteEntity.IndexLayer].Insert(entityDeleteEntity.IndexEntity, entityDeleteEntity.Entity);
            this.targetLayer.Clear();
            this.targetEntity.Clear();
            this.doc.SelectedEntitys = this.list;
        }

        public override void Redo()
        {
            List<EntityBase> deleted = new List<EntityBase>();
            foreach (EntityBase entity in this.list)
            {
                if (entity is EntityLayer)
                {
                    EntityLayer layer = entity as EntityLayer;
                    if (!layer.IsLocked)
                    {
                        int num = this.doc.Layers.IndexOf(layer);
                        this.targetLayer.Add(new UndoRedoEntityDeleteLayer()
                        {
                            Index = num,
                            Layer = layer
                        });
                        deleted.Add(entity);
                    }
                }
                else
                {
                    EntityLayer owner = entity.Parent as EntityLayer;
                    int num1 = this.doc.Layers.IndexOf(owner);
                    int num2 = owner.Children.IndexOf(entity);
                    this.targetEntity.Add(new UndoRedoEntityDeleteEntity()
                    {
                        IndexLayer = num1,
                        Layer = owner,
                        IndexEntity = num2,
                        Entity = entity
                    });
                    deleted.Add(entity);
                }
            }
            foreach (UndoRedoEntityDeleteEntity entityDeleteEntity in this.targetEntity)
                entityDeleteEntity.Layer.Remove(entityDeleteEntity.Entity);
            foreach (UndoRedoEntityDeleteLayer entityDeleteLayer in this.targetLayer)
                this.doc.Layers.Remove(entityDeleteLayer.Layer);
            List<EntityBase> entityList = new List<EntityBase>((IEnumerable<EntityBase>)this.list);
            entityList.RemoveAll((Predicate<EntityBase>)(x => deleted.Contains(x)));
            this.doc.SelectedEntitys = entityList;
        }

        public UndoRedoEntityDelete(DocumentBase doc, List<EntityBase> list)
        {
            this.Name = "Entity Delete";
            this.doc = doc;
            this.list = new List<EntityBase>((IEnumerable<EntityBase>)list);
            this.targetLayer = new List<UndoRedoEntityDeleteLayer>();
            this.targetEntity = new List<UndoRedoEntityDeleteEntity>();
        }
    }
}
