﻿using System.Collections.Generic;

namespace McLaser.EditViewerSk.UndoRedo
{
    internal class UndoRedoMultiple : List<IUndoRedo>, IUndoRedo
    {
        public string Name { get; set; }

        public virtual void Execute()
        {
            foreach (IUndoRedo undoRedo in (List<IUndoRedo>)this)
                undoRedo.Execute();
        }

        public virtual void Undo()
        {
            List<IUndoRedo> undoRedoList = new List<IUndoRedo>((IEnumerable<IUndoRedo>)this);
            undoRedoList.Reverse();
            foreach (IUndoRedo undoRedo in undoRedoList)
                undoRedo.Undo();
        }

        public virtual void Redo()
        {
            foreach (IUndoRedo undoRedo in (List<IUndoRedo>)this)
                undoRedo.Redo();
        }
    }
}
