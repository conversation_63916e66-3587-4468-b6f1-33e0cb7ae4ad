﻿

using System;
using System.ComponentModel;


namespace McLaser.EditViewerSk.Convertors
{
    public class BooleanTypeDescriptionProvider : TypeDescriptionProvider
    {
        private static readonly TypeDescriptionProvider DefaultProvider = TypeDescriptor.GetProvider(typeof(bool));

        public BooleanTypeDescriptionProvider() : base(DefaultProvider) { }

        public override ICustomTypeDescriptor GetTypeDescriptor(Type objectType, object instance)
        {
            var descriptor = base.GetTypeDescriptor(objectType, instance);
            return new BooleanTypeDescriptor(descriptor);
        }

        public static void Register()
        {
            TypeDescriptor.AddProvider(new BooleanTypeDescriptionProvider(), typeof(bool));
        }
    }

}
