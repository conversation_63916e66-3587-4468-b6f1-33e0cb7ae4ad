﻿using McLaser.Core.Services.DeviceService.Base.Motion;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using System;

namespace McLaser.EditViewerSk.Marker
{

    public interface IMarkCommand
    {
        bool MarkInitializtion(IMarkerArg arg);
        bool MarkLinear(IMarkerArg arg, EntityLine line, out string cmd);
        bool MarkArc(IMarkerArg arg, EntityArc arc, out string cmd);
        bool MarkCircle();
        bool MarkBegin();
        bool MarkEnd();

        bool MarkCmd(IMarkerArg arg, EntityCmd entityCmd, out string cmd);


    }

    //激光加工管理器Marker
    public interface IMarker
    {
        int Index { get; }

        string Name { get; set; }

        bool IsReady { get; }

        bool IsBusy { get; }

        bool IsError { get; }

        IMarkerArg MarkerArg { get; set; }

        IMotionCard Card { get; set; }

        double ScannerRotateAngle { get; set; }

        DocumentBase Document { get; }

        bool IsEnablePens { get; set; }

        uint MarkCounts { get; }

        object Tag { get; set; }


        bool Ready(IMarkerArg markerArg);

        bool Clear();

        bool Start(int index);

        bool Stop();

        bool Reset();

        bool IsTargetLayer(EntityLayer layer);

        bool IsTargetEntity(EntityBase entity);

        event MarkerEventHandler OnStarted;

        event MarkerEventHandler OnProgress;

        event MarkerEventHandler OnFailed;

        event MarkerEventHandler OnFinished;

        event EventHandler OnBufferUpdate;

        void NotifyStarted();
        void NotifyFailed();
        void NotifyProgressing();
        void NotifyFinished();
    }
}
