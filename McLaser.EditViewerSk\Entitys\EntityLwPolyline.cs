﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{



    public class EntityLwPolyline : EntityBase
    {

        private bool isClosed;


        public EntityLwPolyline()
        {
            Name = "Polyline";
           new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/217.png"));

        }

        private List<Vector2> _vertexes = new List<Vector2>();
        public List<Vector2> Vertexes
        {
            get { return _vertexes; }
            set { _vertexes = value; this.IsNeedToRegen = true; }
        }
     

        [Browsable(true), ReadOnly(false), Category("Data"), DisplayName("Closed"), RefreshProperties(RefreshProperties.All)]
        public bool IsClosed
        {
            get => this.isClosed;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                this.isClosed = value;
                this.IsNeedToRegen = true;
            }
        }


        [JsonIgnore]
        public override BoundingBox BoundingBox { get; set; } = BoundingBox.Empty;
  


        private void RegenVertextList()
        {
             
        }

        private void RegenBoundRect()
        {
            this.BoundingBox.Clear();

            if (_vertexes.Count > 0)
            {
                double minX = double.MaxValue;
                double minY = double.MaxValue;
                double maxX = double.MinValue;
                double maxY = double.MinValue;

                foreach (Vector2 point in _vertexes)
                {
                    minX = point.X < minX ? point.X : minX;
                    minY = point.Y < minY ? point.Y : minY;

                    maxX = point.X > maxX ? point.X : maxX;
                    maxY = point.Y > maxY ? point.Y : maxY;
                }
                BoundingBox = new BoundingBox(minX, maxY, maxX, minY);
            }
                //foreach (IEntity outline in this.outlineList)
                //this.BoundingBox.Union(outline.BoundRect);
            //this.location = this.BoundRect.LocationByAlign(this.align);
        }

        public override void Regen()
        {
            this.RegenVertextList();
            this.RegenBoundRect();
 
    
            this.IsNeedToRegen = false;
        }





        /// <summary>
        /// 对象捕捉点
        /// </summary>
        public override List<ObjectSnapPoint> GetSnapPoints()
        {
            List<ObjectSnapPoint> snapPnts = new List<ObjectSnapPoint>();
            int numOfVertices = this.Vertexes.Count;
            for (int i = 0; i < numOfVertices; ++i)
            {
                snapPnts.Add(new ObjectSnapPoint(ObjectSnapMode.End, GetPointAt(i)));

                if (i < numOfVertices - 1)
                {
                    snapPnts.Add(
                        new ObjectSnapPoint(ObjectSnapMode.Mid, (GetPointAt(i) + GetPointAt(i + 1)) / 2));
                }


            }

            if (isClosed && numOfVertices > 1)
            {
                snapPnts.Add(
                    new ObjectSnapPoint(ObjectSnapMode.Mid, (GetPointAt(0) + GetPointAt(numOfVertices - 1)) / 2));
            }

            return snapPnts;
        }



        public override void Render(IView view)
        {
            if (view == null)
            {
                return;
            }

            if (!this.IsRenderable)
            {
                return;
            }
            if (this.IsNeedToRegen)
            {
                this.Regen();
            }
            Pen.Color = IsSelected ? SKColors.Red : SKColors.Black;
            int numOfVertices = this.Vertexes.Count;
            for (int i = 0; i < numOfVertices - 1; ++i)
            {
                (view as ViewBase).DrawLine(GetPointAt(i), GetPointAt(i + 1), Pen);
                (view as ViewBase).DrawArrow(GetPointAt(i), GetPointAt(i + 1), 10);
            }

            if (isClosed && numOfVertices > 2)
            {
                (view as ViewBase).DrawLine(GetPointAt(numOfVertices - 1), GetPointAt(0), Pen);
                (view as ViewBase).DrawArrow(GetPointAt(numOfVertices - 1), GetPointAt(0), 10);
            }
        }

        public Vector2 GetPointAt(int index)
        {
            return _vertexes[index];
        }

        public override bool HitTest(double left, double top, double right, double bottom, double threshold)
        {
            return MathHelper.IntersectPolylineInRect(new BoundingBox(left, top, right, bottom), this.Vertexes);
        }

      


        public override void Translate(Vector2 translation)
        {
            for (int i = 0; i < this.Vertexes.Count; ++i)
            {
                _vertexes[i] += translation;
            }
        }

        
    }
}
