using McLaser.Collections;
using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Rendering;
using McLaser.EditViewerSk.Spatial;
using McLaser.Entities;
using McLaser.Tables;
using Newtonsoft.Json;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Numerics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Imaging;

namespace McLaser.EditViewerSk.Entitys
{
    public class EntityLayer : EntityBase
    {
        private SpatialIndexManager _spatialIndex = new SpatialIndexManager();

        public override string ToString() => "Layer: " + this.name;
        protected BoundingBox boundRect = BoundingBox.Empty;
        protected float angle;


        [Browsable(false)]
        public virtual float Angle
        {
            get => this.angle;
            set
            {
                if (this.Parent != null && this.IsLocked)
                    return;
                float angle = value - this.angle;
                if (this.Parent == null)
                    return;
                this.Rotate(angle, angle);
            }
        }





        public EntityLayer()
        {
            this.Parent = null;
            this.IsVisible = true;
            this.IsMarkerable = true;
            this.BoundingBox = BoundingBox.Empty;
            this.IsNeedToRegen = false;
            this.Repeats = 1;
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/layers.png"));
        }

        public EntityLayer(string name)
          : this()
        {
            this.Name = name;
            Icon =  new BitmapImage(new Uri("pack://application:,,,/McLaser.EditViewerSk;component/Views/Icons/Toolbars/layers.png"));
        }


        [JsonIgnore]
        public override BoundingBox BoundingBox
        {
            get
            {
                this.RegenBoundRect();
                return this.boundRect;
            }
            set
            {
                boundRect = value;
            }
        }


        protected virtual void RegenBoundRect()
        {
            this.boundRect.Clear();
            foreach (EntityBase entity in this.Children)
            {
                if (entity.IsVisible)
                    this.boundRect.Union(entity.BoundingBox);
            }
        }


        protected virtual void RegenVertextList()
        {
            foreach (EntityBase entity in this.Children)
                entity.Regen();
        }

        public override void Render(IView view)
        {
            


            //foreach (var entity in DxfEntitys)
            //{
            //    if (entity is Line)
            //    {
            //        Line line = entity as Line;
            //        Vector2 ptStart = new Vector2(line.StartPoint.X, line.StartPoint.Y);
            //        Vector2 ptEnd = new Vector2(line.EndPoint.X, line.EndPoint.Y);
            //        (view as ViewBase).DrawLine(ptStart, ptEnd, Pen);
            //    }
            //    if (entity is Circle)
            //    {
            //        Circle circle = entity as Circle;
            //        Vector2 ptCenter = new Vector2(circle.Center.X, circle.Center.Y);

            //        (view as ViewBase).DrawCircle(ptCenter, circle.Radius, Pen);
            //    }
            //}
            //return;

            foreach (EntityBase entity in Children)
            {
                if (IsNeedToRegen) entity.IsNeedToRegen = IsNeedToRegen;
                entity?.Render(view);
            }
        }

        /// <summary>
        /// 带视口裁剪的渲染方法（使用空间索引优化）
        /// </summary>
        /// <param name="view">视图对象</param>
        /// <param name="viewportBounds">视口边界</param>
        public virtual void RenderWithViewportCulling(IView view, BoundingBox viewportBounds)
        {
            if (viewportBounds == null)
            {
                Render(view);
                return;
            }

            var layerBounds = CalculateLayerBounds();
            if (_spatialIndex.NeedsRebuild(layerBounds))
            {
                _spatialIndex.BuildIndex(Children, layerBounds);
            }

            var visibleEntities = _spatialIndex.Query(viewportBounds);
            int renderedCount = 0;
            int totalCount = Children.Count;

            foreach (var entity in visibleEntities)
            {
                if (IsNeedToRegen) entity.IsNeedToRegen = IsNeedToRegen;
                entity?.Render(view);
                renderedCount++;
            }

#if DEBUG
            System.Diagnostics.Debug.WriteLine($"图层 {Name}: 渲染 {renderedCount}/{totalCount} 个对象 (空间索引优化)");
            System.Diagnostics.Debug.WriteLine($"索引统计: {_spatialIndex.GetIndexStats()}");
#endif
        }

        /// <summary>
        /// 计算图层边界
        /// </summary>
        /// <returns>图层边界</returns>
        private BoundingBox CalculateLayerBounds()
        {
            if (Children.Count == 0)
                return BoundingBox.Empty;

            var bounds = BoundingBox.Empty;
            bool hasValidBounds = false;

            foreach (var entity in Children)
            {
                if (entity?.BoundingBox != null && !entity.BoundingBox.IsEmpty)
                {
                    if (!hasValidBounds)
                    {
                        bounds = new BoundingBox(entity.BoundingBox.Left, entity.BoundingBox.Top, entity.BoundingBox.Right, entity.BoundingBox.Bottom);
                        hasValidBounds = true;
                    }
                    else
                    {
                        bounds.Union(entity.BoundingBox);
                    }
                }
            }

            return hasValidBounds ? bounds : BoundingBox.Empty;
        }

        public override void Regen()
        {
            this.RegenVertextList();
            this.RegenBoundRect();
            this.IsNeedToRegen = false;
            _spatialIndex.MarkDirty();
        }

        public bool Remove(EntityBase entity)
        {
            if (!Children.Contains(entity)) return false;

            Children.Remove(entity);
            _spatialIndex.MarkDirty();
            SortIndex();
            return true;
        }

        public bool Add(EntityBase entity)
        {
            //if (!Children.Contains(entity)) return false;
            Children.Add(entity);
            IsNeedToRegen = true;
            _spatialIndex.MarkDirty();
            SortIndex();
            return true;
        }

        internal void Insert(int index, EntityBase entity)
        {
            if (index < 0 || index > this.Children.Count)
                return;
            this.Children.Insert(index, entity);
            entity.Parent = this;
            SortIndex();
            IsNeedToRegen = true;
        }

        private void SortIndex()
        {
            for (int i = 0; i < Children.Count; i++)
            {
                Children[i].Index = i + 1;
            }
        }


        public override object Clone()
        {
            EntityLayer layer = new EntityLayer()
            {
                Name = this.Name,
                Description = this.Description,
                Parent = this.Parent,
                IsSelected = this.IsSelected,
                IsVisible = this.IsVisible,
                IsLocked = this.IsLocked,
                IsMarkerable = this.IsMarkerable,
                boundRect = this.boundRect.Clone(),
                Repeats = this.Repeats,
                angle = this.angle,
                Tag = this.Tag,
                Index = this.Index,
                IsNeedToRegen = true

            };
            lock (this.SyncRoot)
            {
                foreach (EntityBase entity in Children)
                {
                    if (entity is ICloneable cloneable2)
                    {
                        EntityBase entity2 = (EntityBase)cloneable2.Clone();
                        layer.Add(entity2);
                    }
                }
            }
            return (object)layer;
        }

        public override void Translate(Vector2 translation)
        {
            throw new System.NotImplementedException();
        }

        /// <summary>
        /// 获取当前视图的缩放比例
        /// </summary>
        /// <param name="viewBase">视图基类</param>
        /// <returns>缩放比例</returns>
        private float GetCurrentScale(ViewBase viewBase)
        {
            try
            {
                // 通过坐标变换计算缩放比例
                var point1 = viewBase.ModelToCanvas(new Vector2(0, 0));
                var point2 = viewBase.ModelToCanvas(new Vector2(1, 0));

                var distance = Math.Abs(point2.X - point1.X);
                return (float)distance;
            }
            catch
            {
                return 1.0f;
            }
        }

        /// <summary>
        /// 使用脏区域渲染
        /// </summary>
        public void RenderWithDirtyRegions(IView view, BoundingBox viewport, DirtyRegionManager dirtyManager, RenderCacheManager cacheManager)
        {
            if (!IsVisible) return;

            var dirtyEntities = dirtyManager.GetDirtyEntitiesInViewport(viewport, Children);
            var renderCount = 0;
            var cacheHits = 0;
            var skippedCount = 0;

            foreach (EntityBase entity in Children)
            {
                // 检查实体可见性和边界框有效性
                if (!entity.IsVisible)
                {
                    skippedCount++;
                    continue;
                }

                if (entity.BoundingBox == null || entity.BoundingBox.IsEmpty || !viewport.Intersects(entity.BoundingBox))
                {
                    skippedCount++;
                    continue;
                }

                var cacheKey = cacheManager.GenerateEntityKey(entity);

                if (dirtyEntities.Contains(entity))
                {
                    // 实体是脏的，需要重新渲染
                    entity.Render(view);
                    renderCount++;

                    // 渲染后保存到缓存（如果支持缓存）
                    if (cacheKey != null && view is ViewBase viewBase && viewBase.Canvas != null)
                    {
                        // 获取当前缩放比例
                        var scale = GetCurrentScale(viewBase);

                        // 尝试缓存实体渲染结果
                        if (cacheManager.CacheEntityRender(entity, viewBase.Canvas, scale))
                        {
#if DEBUG
                            System.Diagnostics.Debug.WriteLine($"已缓存实体: {entity.GetType().Name}");
#endif
                        }
                    }
                }
                else
                {
                    // 实体不是脏的，尝试使用缓存
                    if (view is ViewBase viewBase && viewBase.Canvas != null)
                    {
                        var scale = GetCurrentScale(viewBase);

                        // 尝试从缓存绘制实体
                        if (cacheManager.DrawCachedEntity(entity, viewBase.Canvas, scale))
                        {
                            cacheHits++;
                        }
                        else
                        {
                            // 缓存未命中，正常渲染
                            entity.Render(view);
                            renderCount++;

                            // 渲染后保存到缓存
                            if (cacheManager.CacheEntityRender(entity, viewBase.Canvas, scale))
                            {
#if DEBUG
                                System.Diagnostics.Debug.WriteLine($"已缓存实体: {entity.GetType().Name}");
#endif
                            }
                        }
                    }
                    else
                    {
                        // 无法获取画布，直接渲染
                        entity.Render(view);
                        renderCount++;
                    }
                }
            }

#if DEBUG
            System.Diagnostics.Debug.WriteLine($"图层 {Name}: 渲染 {renderCount} 个实体, 缓存命中 {cacheHits} 个, 跳过 {skippedCount} 个");
#endif
        }

        /// <summary>
        /// 从缓存渲染
        /// </summary>
        public void RenderFromCache(IView view, BoundingBox viewport, RenderCacheManager cacheManager)
        {
            if (!IsVisible) return;

            var cacheHits = 0;
            var fallbackRenders = 0;
            var skippedCount = 0;

            foreach (EntityBase entity in Children)
            {
                // 检查实体可见性和边界框有效性
                if (!entity.IsVisible)
                {
                    skippedCount++;
                    continue;
                }

                if (entity.BoundingBox == null || entity.BoundingBox.IsEmpty || !viewport.Intersects(entity.BoundingBox))
                {
                    skippedCount++;
                    continue;
                }

                if (view is ViewBase viewBase && viewBase.Canvas != null)
                {
                    var scale = GetCurrentScale(viewBase);

                    // 尝试从缓存绘制实体
                    if (cacheManager.DrawCachedEntity(entity, viewBase.Canvas, scale))
                    {
                        cacheHits++;
                    }
                    else
                    {
                        // 缓存未命中，回退到正常渲染
                        entity.Render(view);
                        fallbackRenders++;

                        // 渲染后保存到缓存
                        if (cacheManager.CacheEntityRender(entity, viewBase.Canvas, scale))
                        {
#if DEBUG
                            System.Diagnostics.Debug.WriteLine($"已缓存实体: {entity.GetType().Name}");
#endif
                        }
                    }
                }
                else
                {
                    // 无法获取画布，直接渲染
                    entity.Render(view);
                    fallbackRenders++;
                }
            }

#if DEBUG
            if (cacheHits > 0 || fallbackRenders > 0 || skippedCount > 0)
            {
                System.Diagnostics.Debug.WriteLine($"图层 {Name}: 缓存命中 {cacheHits} 个, 回退渲染 {fallbackRenders} 个, 跳过 {skippedCount} 个");
            }
#endif
        }
    }
}
