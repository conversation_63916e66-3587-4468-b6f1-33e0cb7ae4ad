#region McLaser library licensed under the MIT License
// 
//                       <PERSON><PERSON><PERSON><PERSON><PERSON> library
// Copyright (c) <PERSON> (<EMAIL>)
// 
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
// 
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
// 
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.
// 
#endregion

using System;
using System.Collections.Generic;
using McLaser.Objects;
using McLaser.Tables;

namespace McLaser.Entities
{
    /// <summary>
    /// Represents a raster image <see cref="EntityObject">entity</see>.
    /// </summary>
    public class Image :
        EntityObject
    {
        #region delegates and events

        public delegate void ImageDefinitionChangedEventHandler(Image sender, TableObjectChangedEventArgs<ImageDefinition> e);
        public event ImageDefinitionChangedEventHandler ImageDefinitionChanged;
        protected virtual ImageDefinition OnImageDefinitionChangedEvent(ImageDefinition oldImageDefinition, ImageDefinition newImageDefinition)
        {
            ImageDefinitionChangedEventHandler ae = this.ImageDefinitionChanged;
            if (ae != null)
            {
                TableObjectChangedEventArgs<ImageDefinition> eventArgs = new TableObjectChangedEventArgs<ImageDefinition>(oldImageDefinition, newImageDefinition);
                ae(this, eventArgs);
                return eventArgs.NewValue;
            }
            return newImageDefinition;
        }

        #endregion

        #region private fields

        private Vector3 position;
        private Vector2 uvector;
        private Vector2 vvector;
        private double width;
        private double height;
        private ImageDefinition imageDefinition;
        private bool clipping;
        private short brightness;
        private short contrast;
        private short fade;
        private ImageDisplayFlags displayOptions;
        private ClippingBoundary clippingBoundary;

        #endregion

        #region constructors

        internal Image()
            : base(EntityType.Image, DxfObjectCode.Image)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <c>Image</c> class.
        /// </summary>
        /// <param name="imageDefinition">Image definition.</param>
        /// <param name="position">Image <see cref="Vector2">position</see> in world coordinates.</param>
        /// <param name="size">Image <see cref="Vector2">size</see> in world coordinates.</param>
        public Image(ImageDefinition imageDefinition, Vector2 position, Vector2 size)
            : this(imageDefinition, new Vector3(position.X, position.Y, 0.0), size.X, size.Y)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <c>Image</c> class.
        /// </summary>
        /// <param name="imageDefinition">Image definition.</param>
        /// <param name="position">Image <see cref="Vector3">position</see> in world coordinates.</param>
        /// <param name="size">Image <see cref="Vector2">size</see> in world coordinates.</param>
        public Image(ImageDefinition imageDefinition, Vector3 position, Vector2 size)
            : this(imageDefinition, position, size.X, size.Y)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <c>Image</c> class.
        /// </summary>
        /// <param name="imageDefinition">Image definition.</param>
        /// <param name="position">Image <see cref="Vector2">position</see> in world coordinates.</param>
        /// <param name="width">Image width in world coordinates.</param>
        /// <param name="height">Image height in world coordinates.</param>
        public Image(ImageDefinition imageDefinition, Vector2 position, double width, double height)
            : this(imageDefinition, new Vector3(position.X, position.Y, 0.0), width, height)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <c>Image</c> class.
        /// </summary>
        /// <param name="imageDefinition">Image definition.</param>
        /// <param name="position">Image <see cref="Vector3">position</see> in world coordinates.</param>
        /// <param name="width">Image width in world coordinates.</param>
        /// <param name="height">Image height in world coordinates.</param>
        public Image(ImageDefinition imageDefinition, Vector3 position, double width, double height)
            : base(EntityType.Image, DxfObjectCode.Image)
        {
            this.imageDefinition = imageDefinition ?? throw new ArgumentNullException(nameof(imageDefinition));
            this.position = position;
            this.uvector = Vector2.UnitX;
            this.vvector = Vector2.UnitY;
            if (width <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(width), width, "The Image width must be greater than zero.");
            }
            this.width = width;
            if (height <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(height), height, "The Image height must be greater than zero.");
            }
            this.height = height;
            this.clipping = false;
            this.brightness = 50;
            this.contrast = 50;
            this.fade = 0;
            this.displayOptions = ImageDisplayFlags.ShowImage | ImageDisplayFlags.ShowImageWhenNotAlignedWithScreen | ImageDisplayFlags.UseClippingBoundary;
            this.clippingBoundary = new ClippingBoundary(0, 0, imageDefinition.Width, imageDefinition.Height);
        }

        #endregion

        #region public properties

        /// <summary>
        /// Gets or sets the image <see cref="Vector3">position</see> in world coordinates.
        /// </summary>
        public Vector3 Position
        {
            get { return this.position; }
            set { this.position = value; }
        }

        /// <summary>
        /// Gets or sets the image <see cref="Vector2">U-vector</see>.
        /// </summary>
        public Vector2 Uvector
        {
            get { return this.uvector; }
            set
            {
                if (Vector2.Equals(Vector2.Zero, value))
                {
                    throw new ArgumentException("The U vector can not be the zero vector.", nameof(value));
                }

                this.uvector = Vector2.Normalize(value);
            }
        }

        /// <summary>
        /// Gets or sets the image <see cref="Vector2">V-vector</see>.
        /// </summary>
        public Vector2 Vvector
        {
            get { return this.vvector; }
            set
            {
                if (Vector2.Equals(Vector2.Zero, value))
                {
                    throw new ArgumentException("The V vector can not be the zero vector.", nameof(value));
                }

                this.vvector = Vector2.Normalize(value);
            }
        }

        /// <summary>
        /// Gets or sets the height of the image in drawing units.
        /// </summary>
        public double Height
        {
            get { return this.height; }
            set
            {
                if (value <= 0)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), value, "The Image height must be greater than zero.");
                }
                this.height = value;
            }
        }

        /// <summary>
        /// Gets or sets the width of the image in drawing units.
        /// </summary>
        public double Width
        {
            get { return this.width; }
            set
            {
                if (value <= 0)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), value, "The Image width must be greater than zero.");
                }
                this.width = value;
            }
        }

        /// <summary>
        /// Gets or sets the image rotation in degrees.
        /// </summary>
        /// <remarks>The image rotation is the angle of the U-vector.</remarks>
        public double Rotation
        {
            get
            {
                return Vector2.Angle(this.uvector) * MathHelper.RadToDeg;
            }
            set
            {
                List<Vector2> uv = MathHelper.Transform(new List<Vector2> { this.uvector, this.vvector },
                    MathHelper.NormalizeAngle(value) * MathHelper.DegToRad,
                    CoordinateSystem.Object, CoordinateSystem.World);
                this.uvector = uv[0];
                this.vvector = uv[1];
            }
        }

        /// <summary>
        /// Gets the <see cref="ImageDefinition">image definition</see>.
        /// </summary>
        public ImageDefinition Definition
        {
            get { return this.imageDefinition; }
            set
            {
                if (value == null)
                {
                    throw new ArgumentNullException(nameof(value));
                }
                this.imageDefinition = this.OnImageDefinitionChangedEvent(this.imageDefinition, value);
            }
        }

        /// <summary>
        /// Gets or sets the clipping state: false = off, true = on.
        /// </summary>
        public bool Clipping
        {
            get { return this.clipping; }
            set { this.clipping = value; }
        }

        /// <summary>
        /// Gets or sets the brightness value (0-100; default = 50)
        /// </summary>
        public short Brightness
        {
            get { return this.brightness; }
            set
            {
                if (value < 0 || value > 100)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), value, "Accepted brightness values range from 0 to 100.");
                }
                this.brightness = value;
            }
        }

        /// <summary>
        /// Gets or sets the contrast value (0-100; default = 50)
        /// </summary>
        public short Contrast
        {
            get { return this.contrast; }
            set
            {
                if (value < 0 || value > 100)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), value, "Accepted contrast values range from 0 to 100.");
                }
                this.contrast = value;
            }
        }

        /// <summary>
        /// Gets or sets the fade value (0-100; default = 0)
        /// </summary>
        public short Fade
        {
            get { return this.fade; }
            set
            {
                if (value < 0 || value > 100)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), value, "Accepted fade values range from 0 to 100.");
                }
                this.fade = value;
            }
        }

        /// <summary>
        /// Gets or sets the image display options.
        /// </summary>
        public ImageDisplayFlags DisplayOptions
        {
            get { return this.displayOptions; }
            set { this.displayOptions = value; }
        }

        /// <summary>
        /// Gets or sets the image clipping boundary.
        /// </summary>
        /// <remarks>
        /// The vertexes coordinates of the clipping boundary are expressed in local coordinates of the image in pixels.
        /// Set as null to restore the default clipping boundary, full image.
        /// </remarks>
        public ClippingBoundary ClippingBoundary
        {
            get { return this.clippingBoundary; }
            set { this.clippingBoundary = value ?? new ClippingBoundary(0, 0, this.Definition.Width, this.Definition.Height); }
        }

        #endregion

        #region overrides

        /// <summary>
        /// Moves, scales, and/or rotates the current entity given a 3x3 transformation matrix and a translation vector.
        /// </summary>
        /// <param name="transformation">Transformation matrix.</param>
        /// <param name="translation">Translation vector.</param>
        /// <remarks>Matrix3 adopts the convention of using column vectors to represent a transformation matrix.</remarks>
        public override void TransformBy(Matrix3 transformation, Vector3 translation)
        {
            Vector3 newPosition = transformation * this.Position + translation;
            Vector3 newNormal = transformation * this.Normal;
            if (Vector3.Equals(Vector3.Zero, newNormal))
            {
                newNormal = this.Normal;
            }

            Matrix3 transOW = MathHelper.ArbitraryAxis(this.Normal);

            Matrix3 transWO = MathHelper.ArbitraryAxis(newNormal);
            transWO = transWO.Transpose();

            Vector3 v;
            v = transOW * new Vector3(this.Uvector.X * this.Width, this.Uvector.Y * this.Width, 0.0);
            v = transformation * v;
            v = transWO * v;
            Vector2 newUvector = new Vector2(v.X, v.Y);
            
            double newWidth;
            if (Vector2.Equals(Vector2.Zero, newUvector))
            {
                newUvector = this.Uvector;
                newWidth = MathHelper.Epsilon;
            }
            else
            {
                newWidth = newUvector.Modulus();
            }

            v = transOW * new Vector3(this.Vvector.X * this.Height, this.Vvector.Y * this.Height, 0.0);
            v = transformation * v;
            v = transWO * v;
            Vector2 newVvector = new Vector2(v.X, v.Y);

            double newHeight;
            if (Vector2.Equals(Vector2.Zero, newVvector))
            {
                newVvector = this.Uvector;
                newHeight = MathHelper.Epsilon;
            }
            else
            {
                newHeight = newVvector.Modulus();
            }

            this.Position = newPosition;
            this.Normal = newNormal;
            this.Uvector = newUvector;
            this.Vvector = newVvector;
            this.Width = newWidth;
            this.Height = newHeight;
        }

        /// <summary>
        /// Creates a new Image that is a copy of the current instance.
        /// </summary>
        /// <returns>A new Image that is a copy of this instance.</returns>
        public override object Clone()
        {
            Image entity = new Image
            {
                //EntityObject properties
                Layer = (Layer) this.Layer.Clone(),
                Linetype = (Linetype) this.Linetype.Clone(),
                Color = (AciColor) this.Color.Clone(),
                Lineweight = this.Lineweight,
                Transparency = (Transparency) this.Transparency.Clone(),
                LinetypeScale = this.LinetypeScale,
                Normal = this.Normal,
                IsVisible = this.IsVisible,
                //Image properties
                Position = this.position,
                Height = this.height,
                Width = this.width,
                Uvector = this.uvector,
                Vvector = this.vvector,
                //Rotation = this.rotation,
                Definition = (ImageDefinition) this.imageDefinition.Clone(),
                Clipping = this.clipping,
                Brightness = this.brightness,
                Contrast = this.contrast,
                Fade = this.fade,
                DisplayOptions = this.displayOptions,
                ClippingBoundary = (ClippingBoundary) this.clippingBoundary.Clone()
            };

            foreach (XData data in this.XData.Values)
            {
                entity.XData.Add((XData) data.Clone());
            }

            return entity;
        }

        #endregion
    }
}