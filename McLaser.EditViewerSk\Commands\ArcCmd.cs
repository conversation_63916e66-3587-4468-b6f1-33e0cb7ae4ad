﻿using McLaser.EditViewerSk.Base;
using McLaser.EditViewerSk.Entitys;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;


namespace McLaser.EditViewerSk.Commands
{
    internal class ArcCmd : DrawCmd
    {
        /// <summary>
        /// 绘制的圆弧
        /// </summary>
        private EntityArc _arc = null;
        private Vector2 tmpPoint = Vector2.Zero;
        private DocumentBase doc;


        public ArcCmd(DocumentBase doc)
        {
            this.doc = doc;
        }
        protected override IEnumerable<EntityBase> newEntities
        {
            get { return new EntityArc[1] { _arc }; }
        }

   
        private Step _step = Step.Step1_SpecifyCenter;
        private enum Step
        {
            Step1_SpecifyCenter = 1,
            Step2_SpecityStartPoint = 2,
            Step3_SpecifyEndPoint = 3,
        }

     
        public override void Initialize()
        {
            base.Initialize();

            //
            _step = Step.Step1_SpecifyCenter;
            this.pointer.Mode = IndicatorMode.Locate;
        }

        protected override void Commit()
        {
            if (this.newEntities.Count() == 1)
            {
                doc.Action.ActEntityAdd(newEntities.First());
            }
        }


        public override EventResult OnMouseDown(MouseEventArgs e)
        {
            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    if (e.LeftButton == MouseButtonState.Pressed)
                    {
                        _arc = new EntityArc();
                        _arc.Center = this.pointer.CurrentSnapPoint;
                        _arc.Radius = 0;
                        _step = Step.Step2_SpecityStartPoint;
                    }
                    break;

                case Step.Step2_SpecityStartPoint:

                    if (e.LeftButton == MouseButtonState.Pressed)
                    {
                        tmpPoint = this.pointer.CurrentSnapPoint;
                        _arc.Radius = (_arc.Center - tmpPoint).Length;

                        double startAngle = MathHelper.SignedAngleInRadian(
                            new Vector2(1, 0),
                            tmpPoint - _arc.Center);
                        startAngle = MathHelper.NormalizeRadianAngle(startAngle);
                        startAngle = MathHelper.ConvertRadiansToDegrees(startAngle);
                        _arc.StartAngle = (float)startAngle;
                        _arc.SweepAngle = 0;

                        _step = Step.Step3_SpecifyEndPoint;
                    }
                    break;

                case Step.Step3_SpecifyEndPoint:

                    if (e.LeftButton == MouseButtonState.Pressed)
                    {
                        double endAngle = MathHelper.SignedAngleInRadian(
                            new Vector2(1, 0),
                            this.pointer.CurrentSnapPoint - _arc.Center);
                        endAngle = MathHelper.NormalizeRadianAngle(endAngle);
                        endAngle = MathHelper.ConvertRadiansToDegrees(endAngle);
                        _arc.SweepAngle = endAngle > _arc.StartAngle ? (float)(endAngle - _arc.StartAngle) : (float)(360.0 + endAngle - _arc.StartAngle);
                        //_arc.SweepAngle = (float)endAngle;

                        _mgr.FinishCurrentCommand();
                    }
                    break;
            }

            return EventResult.Handled;
        }

        public override EventResult OnMouseUp(MouseEventArgs e)
        {
            return EventResult.Handled;
        }

        public override EventResult OnMouseMove(MouseEventArgs e)
        {
            if (e.MiddleButton == MouseButtonState.Pressed)
            {
                return EventResult.Handled;
            }

            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    break;

                case Step.Step2_SpecityStartPoint:
                    break;
                case Step.Step3_SpecifyEndPoint:
                    double endAngle = MathHelper.SignedAngleInRadian(
                            new Vector2(1, 0),
                            this.pointer.CurrentSnapPoint - _arc.Center);
                    endAngle = MathHelper.NormalizeRadianAngle(endAngle);
                    endAngle = MathHelper.ConvertRadiansToDegrees(endAngle);
                    _arc.SweepAngle = endAngle > _arc.StartAngle ? (float)(endAngle - _arc.StartAngle) : (float)(360.0 + endAngle - _arc.StartAngle);
                    //_arc.SweepAngle = (float)endAngle;
                    break;
            }

            return EventResult.Handled;
        }

        public override void OnPaint(ViewBase _viewer)
        {
            if (_arc != null)
            {
                _arc.Render(_viewer);
            }

            switch (_step)
            {
                case Step.Step1_SpecifyCenter:
                    break;

                case Step.Step2_SpecityStartPoint:
                    _viewer.DrawLine(
                        _arc.Center,
                        this.pointer.CurrentSnapPoint, new SKPaint() { Color = SKColors.Black, IsAntialias = true });
                    break;

                case Step.Step3_SpecifyEndPoint:
                    _viewer.DrawLine(
                       _arc.Center,
                       tmpPoint, new SKPaint() { Color = SKColors.Black, IsAntialias = true });

                    _viewer.DrawLine(
                        _arc.Center,
                        this.pointer.CurrentSnapPoint, new SKPaint() { Color = SKColors.Black, IsAntialias = true });

                    break;
            }
        }
    }
}
